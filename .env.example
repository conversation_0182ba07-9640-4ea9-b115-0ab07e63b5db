# Database
POSTGRES_PASSWORD=your_secure_postgres_password
DATABASE_URL=postgresql://postgres:your_secure_postgres_password@localhost:5432/pepe_cas

# Redis
REDIS_PASSWORD=your_secure_redis_password
REDIS_URL=redis://:your_secure_redis_password@localhost:6379

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_BOT_SECRET=your_telegram_bot_secret

# Security
JWT_SECRET=your_very_secure_jwt_secret_key_at_least_32_characters

# Admin Access (comma-separated Telegram IDs)
ADMIN_TELEGRAM_IDS=123456789,987654321

# External APIs
TONNEL_API_URL=https://api.tonnel.io/v1
PORTAL_API_URL=https://portal.gg/api
COINGECKO_API_URL=https://api.coingecko.com/api/v3

# TON Network
TON_NETWORK=testnet
TON_API_KEY=your_ton_api_key

# Game Settings
CRASH_INTERVAL=30
DOUBLE_INTERVAL=15
COINFLIP_TIMEOUT=300

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Game Economics
HOUSE_EDGE=0.02
MAX_BET_PERCENTAGE=0.2

# Double Game Probabilities
DOUBLE_RED_PROBABILITY=0.47
DOUBLE_BLACK_PROBABILITY=0.47
DOUBLE_GREEN_PROBABILITY=0.06

# Crash Game Settings
CRASH_SIGMA=1.5

# Gift Processing
GIFT_WATCHER_INTERVAL=30000
PRICE_UPDATE_INTERVAL="0 0 * * *"

# CORS
ALLOWED_ORIGINS=https://t.me,https://web.telegram.org

# Environment
NODE_ENV=development
PORT=3000
