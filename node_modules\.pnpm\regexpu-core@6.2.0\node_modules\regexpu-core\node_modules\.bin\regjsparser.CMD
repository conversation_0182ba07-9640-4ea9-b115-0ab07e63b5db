@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\regjsparser@0.12.0\node_modules\regjsparser\bin\node_modules;C:\PEPE CAS\node_modules\.pnpm\regjsparser@0.12.0\node_modules\regjsparser\node_modules;C:\PEPE CAS\node_modules\.pnpm\regjsparser@0.12.0\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\regjsparser@0.12.0\node_modules\regjsparser\bin\node_modules;C:\PEPE CAS\node_modules\.pnpm\regjsparser@0.12.0\node_modules\regjsparser\node_modules;C:\PEPE CAS\node_modules\.pnpm\regjsparser@0.12.0\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\regjsparser@0.12.0\node_modules\regjsparser\bin\parser" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\regjsparser@0.12.0\node_modules\regjsparser\bin\parser" %*
)
