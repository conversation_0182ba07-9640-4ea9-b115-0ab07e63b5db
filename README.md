# PEPE CAS - Telegram Mini App Casino

A comprehensive Telegram Mini App casino platform where users can deposit NFT gifts or TON coins, play games like Crash, PvP Coinflip, and Double, and withdraw winnings as NFT gifts.

## 🎯 Features

- **Multi-Game Platform**: Crash, Double (Roulette), and PvP Coinflip
- **NFT Gift Integration**: Deposit and withdraw using Telegram Gifts
- **TON Connect**: Native TON blockchain integration
- **Real-time Gaming**: WebSocket-powered live games
- **Provably Fair**: Cryptographically secure random number generation
- **Admin Panel**: Comprehensive management interface
- **Mobile-First**: Optimized for Telegram Mini Apps

## 🏗️ Architecture

### Tech Stack
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS + shadcn/ui
- **Backend**: Node.js 18 + Express + WebSocket + Prisma ORM
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis + BullMQ
- **Blockchain**: TON Connect v2
- **Infrastructure**: <PERSON>er + <PERSON>inx + Watchtower

### Project Structure
```
pepe-cas/
├── packages/
│   ├── backend/          # Node.js API server
│   ├── frontend/         # React Mini App
│   ├── shared/           # Shared types and utilities
│   └── infra/            # Docker and deployment configs
└── docs/                 # Documentation
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- pnpm 8+
- Docker & Docker Compose
- PostgreSQL 15 (for local development)
- Redis (for local development)

### 1. Clone and Install
```bash
git clone <repository-url>
cd pepe-cas
cp .env.example .env
# Edit .env with your configuration
pnpm install
```

### 2. Database Setup
```bash
cd packages/backend
pnpm prisma migrate dev
pnpm prisma generate
```

### 3. Development
```bash
# Start all services in development mode
pnpm dev

# Or start individually:
cd packages/backend && pnpm dev
cd packages/frontend && pnpm dev
```

### 4. Production Deployment
```bash
cd packages/infra
docker-compose up -d
```

## 🎮 Game Mechanics

### Crash Game
- Players bet on a multiplier that increases over time
- Cash out before the crash to win
- Uses Rayleigh distribution for provably fair results

### Double (Roulette)
- Bet on RED (2x), BLACK (2x), or GREEN (14x)
- 15-second betting rounds
- Configurable probabilities

### Coinflip PvP
- Player A creates a room with a stake
- Player B joins with the same stake
- Winner takes 1.8x (10% house edge)
- Commit-reveal scheme for fairness

## 🔧 Configuration

### Environment Variables
See `.env.example` for all available configuration options.

Key settings:
- `TELEGRAM_BOT_TOKEN`: Your Telegram bot token
- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: Secret for JWT token signing
- `ADMIN_TELEGRAM_IDS`: Comma-separated admin Telegram IDs

### Game Economics
- `HOUSE_EDGE`: Default house edge (2%)
- `MAX_BET_PERCENTAGE`: Max bet as % of bankroll (20%)
- `DOUBLE_*_PROBABILITY`: Roulette color probabilities

## 📡 API Documentation

### Authentication
All API endpoints require JWT authentication via `Authorization: Bearer <token>` header.

### Key Endpoints
- `POST /api/auth/login` - Authenticate with Telegram WebApp data
- `GET /api/balance` - Get user balance and transactions
- `GET /api/coinflip/rooms` - List coinflip rooms
- `POST /api/coinflip/rooms` - Create new coinflip room

### WebSocket Namespaces
- `wss://domain/crash` - Crash game events
- `wss://domain/double` - Double game events
- `wss://domain/coinflip` - Coinflip events

## 🔒 Security Features

- **Rate Limiting**: API and WebSocket rate limits
- **CORS Protection**: Whitelist for Telegram domains
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Prisma ORM with parameterized queries
- **XSS Protection**: Content Security Policy headers
- **Authentication**: JWT with Telegram WebApp verification

## 🧪 Testing

```bash
# Run unit tests
pnpm test

# Run API tests
cd packages/backend && pnpm test

# Run load tests
cd packages/infra && k6 run k6-loadtest.js
```

## 📊 Monitoring

### Health Checks
- `/health` - Application health status
- Docker health checks for all services
- Automatic service restart on failure

### Logging
- Structured logging with Pino
- Request/response logging
- Error tracking and alerting

## 🚢 Deployment

### Docker Compose (Recommended)
```bash
cd packages/infra
docker-compose up -d
```

### Manual Deployment
1. Build backend: `cd packages/backend && pnpm build`
2. Build frontend: `cd packages/frontend && pnpm build`
3. Run migrations: `pnpm prisma migrate deploy`
4. Start services with PM2 or similar

### CI/CD
GitHub Actions workflow included for:
- Automated testing
- Docker image building
- Deployment to production

## 🔄 Development Workflow

### Adding New Games
1. Create game engine in `packages/backend/src/services/`
2. Add WebSocket handlers in `packages/backend/src/sockets/`
3. Create frontend components in `packages/frontend/src/pages/`
4. Update shared types in `packages/shared/types/`

### Database Changes
1. Modify `packages/backend/prisma/schema.prisma`
2. Run `pnpm prisma migrate dev`
3. Update TypeScript types if needed

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in `/docs`

---

**⚠️ Important**: This is a gambling application. Ensure compliance with local laws and regulations before deployment.
