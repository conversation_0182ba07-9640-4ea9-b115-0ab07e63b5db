
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model Price
 * 
 */
export type Price = $Result.DefaultSelection<Prisma.$PricePayload>
/**
 * Model Transaction
 * 
 */
export type Transaction = $Result.DefaultSelection<Prisma.$TransactionPayload>
/**
 * Model CoinflipRoom
 * 
 */
export type CoinflipRoom = $Result.DefaultSelection<Prisma.$CoinflipRoomPayload>
/**
 * Model CrashRound
 * 
 */
export type CrashRound = $Result.DefaultSelection<Prisma.$CrashRoundPayload>
/**
 * Model CrashBet
 * 
 */
export type CrashBet = $Result.DefaultSelection<Prisma.$CrashBetPayload>
/**
 * Model DoubleRound
 * 
 */
export type DoubleRound = $Result.DefaultSelection<Prisma.$DoubleRoundPayload>
/**
 * Model DoubleBet
 * 
 */
export type DoubleBet = $Result.DefaultSelection<Prisma.$DoubleBetPayload>

/**
 * ##  Prisma Client ʲˢ
 * 
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   * 
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): void;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb, ExtArgs>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs>;

  /**
   * `prisma.price`: Exposes CRUD operations for the **Price** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Prices
    * const prices = await prisma.price.findMany()
    * ```
    */
  get price(): Prisma.PriceDelegate<ExtArgs>;

  /**
   * `prisma.transaction`: Exposes CRUD operations for the **Transaction** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Transactions
    * const transactions = await prisma.transaction.findMany()
    * ```
    */
  get transaction(): Prisma.TransactionDelegate<ExtArgs>;

  /**
   * `prisma.coinflipRoom`: Exposes CRUD operations for the **CoinflipRoom** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more CoinflipRooms
    * const coinflipRooms = await prisma.coinflipRoom.findMany()
    * ```
    */
  get coinflipRoom(): Prisma.CoinflipRoomDelegate<ExtArgs>;

  /**
   * `prisma.crashRound`: Exposes CRUD operations for the **CrashRound** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more CrashRounds
    * const crashRounds = await prisma.crashRound.findMany()
    * ```
    */
  get crashRound(): Prisma.CrashRoundDelegate<ExtArgs>;

  /**
   * `prisma.crashBet`: Exposes CRUD operations for the **CrashBet** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more CrashBets
    * const crashBets = await prisma.crashBet.findMany()
    * ```
    */
  get crashBet(): Prisma.CrashBetDelegate<ExtArgs>;

  /**
   * `prisma.doubleRound`: Exposes CRUD operations for the **DoubleRound** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more DoubleRounds
    * const doubleRounds = await prisma.doubleRound.findMany()
    * ```
    */
  get doubleRound(): Prisma.DoubleRoundDelegate<ExtArgs>;

  /**
   * `prisma.doubleBet`: Exposes CRUD operations for the **DoubleBet** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more DoubleBets
    * const doubleBets = await prisma.doubleBet.findMany()
    * ```
    */
  get doubleBet(): Prisma.DoubleBetDelegate<ExtArgs>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError
  export import NotFoundError = runtime.NotFoundError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics 
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 5.22.0
   * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion 

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? K : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    Price: 'Price',
    Transaction: 'Transaction',
    CoinflipRoom: 'CoinflipRoom',
    CrashRound: 'CrashRound',
    CrashBet: 'CrashBet',
    DoubleRound: 'DoubleRound',
    DoubleBet: 'DoubleBet'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb extends $Utils.Fn<{extArgs: $Extensions.InternalArgs, clientOptions: PrismaClientOptions }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], this['params']['clientOptions']>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, ClientOptions = {}> = {
    meta: {
      modelProps: "user" | "price" | "transaction" | "coinflipRoom" | "crashRound" | "crashBet" | "doubleRound" | "doubleBet"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      Price: {
        payload: Prisma.$PricePayload<ExtArgs>
        fields: Prisma.PriceFieldRefs
        operations: {
          findUnique: {
            args: Prisma.PriceFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.PriceFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload>
          }
          findFirst: {
            args: Prisma.PriceFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.PriceFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload>
          }
          findMany: {
            args: Prisma.PriceFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload>[]
          }
          create: {
            args: Prisma.PriceCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload>
          }
          createMany: {
            args: Prisma.PriceCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.PriceCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload>[]
          }
          delete: {
            args: Prisma.PriceDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload>
          }
          update: {
            args: Prisma.PriceUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload>
          }
          deleteMany: {
            args: Prisma.PriceDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.PriceUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.PriceUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PricePayload>
          }
          aggregate: {
            args: Prisma.PriceAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregatePrice>
          }
          groupBy: {
            args: Prisma.PriceGroupByArgs<ExtArgs>
            result: $Utils.Optional<PriceGroupByOutputType>[]
          }
          count: {
            args: Prisma.PriceCountArgs<ExtArgs>
            result: $Utils.Optional<PriceCountAggregateOutputType> | number
          }
        }
      }
      Transaction: {
        payload: Prisma.$TransactionPayload<ExtArgs>
        fields: Prisma.TransactionFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TransactionFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TransactionFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload>
          }
          findFirst: {
            args: Prisma.TransactionFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TransactionFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload>
          }
          findMany: {
            args: Prisma.TransactionFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload>[]
          }
          create: {
            args: Prisma.TransactionCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload>
          }
          createMany: {
            args: Prisma.TransactionCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TransactionCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload>[]
          }
          delete: {
            args: Prisma.TransactionDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload>
          }
          update: {
            args: Prisma.TransactionUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload>
          }
          deleteMany: {
            args: Prisma.TransactionDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TransactionUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.TransactionUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TransactionPayload>
          }
          aggregate: {
            args: Prisma.TransactionAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTransaction>
          }
          groupBy: {
            args: Prisma.TransactionGroupByArgs<ExtArgs>
            result: $Utils.Optional<TransactionGroupByOutputType>[]
          }
          count: {
            args: Prisma.TransactionCountArgs<ExtArgs>
            result: $Utils.Optional<TransactionCountAggregateOutputType> | number
          }
        }
      }
      CoinflipRoom: {
        payload: Prisma.$CoinflipRoomPayload<ExtArgs>
        fields: Prisma.CoinflipRoomFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CoinflipRoomFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CoinflipRoomFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload>
          }
          findFirst: {
            args: Prisma.CoinflipRoomFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CoinflipRoomFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload>
          }
          findMany: {
            args: Prisma.CoinflipRoomFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload>[]
          }
          create: {
            args: Prisma.CoinflipRoomCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload>
          }
          createMany: {
            args: Prisma.CoinflipRoomCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.CoinflipRoomCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload>[]
          }
          delete: {
            args: Prisma.CoinflipRoomDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload>
          }
          update: {
            args: Prisma.CoinflipRoomUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload>
          }
          deleteMany: {
            args: Prisma.CoinflipRoomDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CoinflipRoomUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.CoinflipRoomUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CoinflipRoomPayload>
          }
          aggregate: {
            args: Prisma.CoinflipRoomAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateCoinflipRoom>
          }
          groupBy: {
            args: Prisma.CoinflipRoomGroupByArgs<ExtArgs>
            result: $Utils.Optional<CoinflipRoomGroupByOutputType>[]
          }
          count: {
            args: Prisma.CoinflipRoomCountArgs<ExtArgs>
            result: $Utils.Optional<CoinflipRoomCountAggregateOutputType> | number
          }
        }
      }
      CrashRound: {
        payload: Prisma.$CrashRoundPayload<ExtArgs>
        fields: Prisma.CrashRoundFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CrashRoundFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CrashRoundFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload>
          }
          findFirst: {
            args: Prisma.CrashRoundFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CrashRoundFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload>
          }
          findMany: {
            args: Prisma.CrashRoundFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload>[]
          }
          create: {
            args: Prisma.CrashRoundCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload>
          }
          createMany: {
            args: Prisma.CrashRoundCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.CrashRoundCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload>[]
          }
          delete: {
            args: Prisma.CrashRoundDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload>
          }
          update: {
            args: Prisma.CrashRoundUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload>
          }
          deleteMany: {
            args: Prisma.CrashRoundDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CrashRoundUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.CrashRoundUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashRoundPayload>
          }
          aggregate: {
            args: Prisma.CrashRoundAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateCrashRound>
          }
          groupBy: {
            args: Prisma.CrashRoundGroupByArgs<ExtArgs>
            result: $Utils.Optional<CrashRoundGroupByOutputType>[]
          }
          count: {
            args: Prisma.CrashRoundCountArgs<ExtArgs>
            result: $Utils.Optional<CrashRoundCountAggregateOutputType> | number
          }
        }
      }
      CrashBet: {
        payload: Prisma.$CrashBetPayload<ExtArgs>
        fields: Prisma.CrashBetFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CrashBetFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CrashBetFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload>
          }
          findFirst: {
            args: Prisma.CrashBetFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CrashBetFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload>
          }
          findMany: {
            args: Prisma.CrashBetFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload>[]
          }
          create: {
            args: Prisma.CrashBetCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload>
          }
          createMany: {
            args: Prisma.CrashBetCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.CrashBetCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload>[]
          }
          delete: {
            args: Prisma.CrashBetDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload>
          }
          update: {
            args: Prisma.CrashBetUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload>
          }
          deleteMany: {
            args: Prisma.CrashBetDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CrashBetUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.CrashBetUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CrashBetPayload>
          }
          aggregate: {
            args: Prisma.CrashBetAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateCrashBet>
          }
          groupBy: {
            args: Prisma.CrashBetGroupByArgs<ExtArgs>
            result: $Utils.Optional<CrashBetGroupByOutputType>[]
          }
          count: {
            args: Prisma.CrashBetCountArgs<ExtArgs>
            result: $Utils.Optional<CrashBetCountAggregateOutputType> | number
          }
        }
      }
      DoubleRound: {
        payload: Prisma.$DoubleRoundPayload<ExtArgs>
        fields: Prisma.DoubleRoundFieldRefs
        operations: {
          findUnique: {
            args: Prisma.DoubleRoundFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.DoubleRoundFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload>
          }
          findFirst: {
            args: Prisma.DoubleRoundFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.DoubleRoundFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload>
          }
          findMany: {
            args: Prisma.DoubleRoundFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload>[]
          }
          create: {
            args: Prisma.DoubleRoundCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload>
          }
          createMany: {
            args: Prisma.DoubleRoundCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.DoubleRoundCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload>[]
          }
          delete: {
            args: Prisma.DoubleRoundDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload>
          }
          update: {
            args: Prisma.DoubleRoundUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload>
          }
          deleteMany: {
            args: Prisma.DoubleRoundDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.DoubleRoundUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.DoubleRoundUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleRoundPayload>
          }
          aggregate: {
            args: Prisma.DoubleRoundAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateDoubleRound>
          }
          groupBy: {
            args: Prisma.DoubleRoundGroupByArgs<ExtArgs>
            result: $Utils.Optional<DoubleRoundGroupByOutputType>[]
          }
          count: {
            args: Prisma.DoubleRoundCountArgs<ExtArgs>
            result: $Utils.Optional<DoubleRoundCountAggregateOutputType> | number
          }
        }
      }
      DoubleBet: {
        payload: Prisma.$DoubleBetPayload<ExtArgs>
        fields: Prisma.DoubleBetFieldRefs
        operations: {
          findUnique: {
            args: Prisma.DoubleBetFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.DoubleBetFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload>
          }
          findFirst: {
            args: Prisma.DoubleBetFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.DoubleBetFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload>
          }
          findMany: {
            args: Prisma.DoubleBetFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload>[]
          }
          create: {
            args: Prisma.DoubleBetCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload>
          }
          createMany: {
            args: Prisma.DoubleBetCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.DoubleBetCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload>[]
          }
          delete: {
            args: Prisma.DoubleBetDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload>
          }
          update: {
            args: Prisma.DoubleBetUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload>
          }
          deleteMany: {
            args: Prisma.DoubleBetDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.DoubleBetUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.DoubleBetUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DoubleBetPayload>
          }
          aggregate: {
            args: Prisma.DoubleBetAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateDoubleBet>
          }
          groupBy: {
            args: Prisma.DoubleBetGroupByArgs<ExtArgs>
            result: $Utils.Optional<DoubleBetGroupByOutputType>[]
          }
          count: {
            args: Prisma.DoubleBetCountArgs<ExtArgs>
            result: $Utils.Optional<DoubleBetCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
  }


  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    transactions: number
    coinflipsA: number
    coinflipsB: number
    crashBets: number
    doubleBets: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    transactions?: boolean | UserCountOutputTypeCountTransactionsArgs
    coinflipsA?: boolean | UserCountOutputTypeCountCoinflipsAArgs
    coinflipsB?: boolean | UserCountOutputTypeCountCoinflipsBArgs
    crashBets?: boolean | UserCountOutputTypeCountCrashBetsArgs
    doubleBets?: boolean | UserCountOutputTypeCountDoubleBetsArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountTransactionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TransactionWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountCoinflipsAArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CoinflipRoomWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountCoinflipsBArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CoinflipRoomWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountCrashBetsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CrashBetWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountDoubleBetsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: DoubleBetWhereInput
  }


  /**
   * Count Type CrashRoundCountOutputType
   */

  export type CrashRoundCountOutputType = {
    bets: number
  }

  export type CrashRoundCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    bets?: boolean | CrashRoundCountOutputTypeCountBetsArgs
  }

  // Custom InputTypes
  /**
   * CrashRoundCountOutputType without action
   */
  export type CrashRoundCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRoundCountOutputType
     */
    select?: CrashRoundCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * CrashRoundCountOutputType without action
   */
  export type CrashRoundCountOutputTypeCountBetsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CrashBetWhereInput
  }


  /**
   * Count Type DoubleRoundCountOutputType
   */

  export type DoubleRoundCountOutputType = {
    bets: number
  }

  export type DoubleRoundCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    bets?: boolean | DoubleRoundCountOutputTypeCountBetsArgs
  }

  // Custom InputTypes
  /**
   * DoubleRoundCountOutputType without action
   */
  export type DoubleRoundCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRoundCountOutputType
     */
    select?: DoubleRoundCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * DoubleRoundCountOutputType without action
   */
  export type DoubleRoundCountOutputTypeCountBetsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: DoubleBetWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserAvgAggregateOutputType = {
    id: number | null
    balanceTon: Decimal | null
  }

  export type UserSumAggregateOutputType = {
    id: number | null
    balanceTon: Decimal | null
  }

  export type UserMinAggregateOutputType = {
    id: number | null
    telegramId: string | null
    username: string | null
    balanceTon: Decimal | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: number | null
    telegramId: string | null
    username: string | null
    balanceTon: Decimal | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    telegramId: number
    username: number
    balanceTon: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserAvgAggregateInputType = {
    id?: true
    balanceTon?: true
  }

  export type UserSumAggregateInputType = {
    id?: true
    balanceTon?: true
  }

  export type UserMinAggregateInputType = {
    id?: true
    telegramId?: true
    username?: true
    balanceTon?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    telegramId?: true
    username?: true
    balanceTon?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    telegramId?: true
    username?: true
    balanceTon?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: UserAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: UserSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _avg?: UserAvgAggregateInputType
    _sum?: UserSumAggregateInputType
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: number
    telegramId: string
    username: string | null
    balanceTon: Decimal
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    telegramId?: boolean
    username?: boolean
    balanceTon?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    transactions?: boolean | User$transactionsArgs<ExtArgs>
    coinflipsA?: boolean | User$coinflipsAArgs<ExtArgs>
    coinflipsB?: boolean | User$coinflipsBArgs<ExtArgs>
    crashBets?: boolean | User$crashBetsArgs<ExtArgs>
    doubleBets?: boolean | User$doubleBetsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    telegramId?: boolean
    username?: boolean
    balanceTon?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    telegramId?: boolean
    username?: boolean
    balanceTon?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    transactions?: boolean | User$transactionsArgs<ExtArgs>
    coinflipsA?: boolean | User$coinflipsAArgs<ExtArgs>
    coinflipsB?: boolean | User$coinflipsBArgs<ExtArgs>
    crashBets?: boolean | User$crashBetsArgs<ExtArgs>
    doubleBets?: boolean | User$doubleBetsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      transactions: Prisma.$TransactionPayload<ExtArgs>[]
      coinflipsA: Prisma.$CoinflipRoomPayload<ExtArgs>[]
      coinflipsB: Prisma.$CoinflipRoomPayload<ExtArgs>[]
      crashBets: Prisma.$CrashBetPayload<ExtArgs>[]
      doubleBets: Prisma.$DoubleBetPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      telegramId: string
      username: string | null
      balanceTon: Prisma.Decimal
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    transactions<T extends User$transactionsArgs<ExtArgs> = {}>(args?: Subset<T, User$transactionsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "findMany"> | Null>
    coinflipsA<T extends User$coinflipsAArgs<ExtArgs> = {}>(args?: Subset<T, User$coinflipsAArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "findMany"> | Null>
    coinflipsB<T extends User$coinflipsBArgs<ExtArgs> = {}>(args?: Subset<T, User$coinflipsBArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "findMany"> | Null>
    crashBets<T extends User$crashBetsArgs<ExtArgs> = {}>(args?: Subset<T, User$crashBetsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "findMany"> | Null>
    doubleBets<T extends User$doubleBetsArgs<ExtArgs> = {}>(args?: Subset<T, User$doubleBetsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */ 
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'Int'>
    readonly telegramId: FieldRef<"User", 'String'>
    readonly username: FieldRef<"User", 'String'>
    readonly balanceTon: FieldRef<"User", 'Decimal'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
  }

  /**
   * User.transactions
   */
  export type User$transactionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    where?: TransactionWhereInput
    orderBy?: TransactionOrderByWithRelationInput | TransactionOrderByWithRelationInput[]
    cursor?: TransactionWhereUniqueInput
    take?: number
    skip?: number
    distinct?: TransactionScalarFieldEnum | TransactionScalarFieldEnum[]
  }

  /**
   * User.coinflipsA
   */
  export type User$coinflipsAArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    where?: CoinflipRoomWhereInput
    orderBy?: CoinflipRoomOrderByWithRelationInput | CoinflipRoomOrderByWithRelationInput[]
    cursor?: CoinflipRoomWhereUniqueInput
    take?: number
    skip?: number
    distinct?: CoinflipRoomScalarFieldEnum | CoinflipRoomScalarFieldEnum[]
  }

  /**
   * User.coinflipsB
   */
  export type User$coinflipsBArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    where?: CoinflipRoomWhereInput
    orderBy?: CoinflipRoomOrderByWithRelationInput | CoinflipRoomOrderByWithRelationInput[]
    cursor?: CoinflipRoomWhereUniqueInput
    take?: number
    skip?: number
    distinct?: CoinflipRoomScalarFieldEnum | CoinflipRoomScalarFieldEnum[]
  }

  /**
   * User.crashBets
   */
  export type User$crashBetsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    where?: CrashBetWhereInput
    orderBy?: CrashBetOrderByWithRelationInput | CrashBetOrderByWithRelationInput[]
    cursor?: CrashBetWhereUniqueInput
    take?: number
    skip?: number
    distinct?: CrashBetScalarFieldEnum | CrashBetScalarFieldEnum[]
  }

  /**
   * User.doubleBets
   */
  export type User$doubleBetsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    where?: DoubleBetWhereInput
    orderBy?: DoubleBetOrderByWithRelationInput | DoubleBetOrderByWithRelationInput[]
    cursor?: DoubleBetWhereUniqueInput
    take?: number
    skip?: number
    distinct?: DoubleBetScalarFieldEnum | DoubleBetScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model Price
   */

  export type AggregatePrice = {
    _count: PriceCountAggregateOutputType | null
    _avg: PriceAvgAggregateOutputType | null
    _sum: PriceSumAggregateOutputType | null
    _min: PriceMinAggregateOutputType | null
    _max: PriceMaxAggregateOutputType | null
  }

  export type PriceAvgAggregateOutputType = {
    minTon: Decimal | null
  }

  export type PriceSumAggregateOutputType = {
    minTon: Decimal | null
  }

  export type PriceMinAggregateOutputType = {
    modelKey: string | null
    minTon: Decimal | null
    updatedAt: Date | null
  }

  export type PriceMaxAggregateOutputType = {
    modelKey: string | null
    minTon: Decimal | null
    updatedAt: Date | null
  }

  export type PriceCountAggregateOutputType = {
    modelKey: number
    minTon: number
    updatedAt: number
    _all: number
  }


  export type PriceAvgAggregateInputType = {
    minTon?: true
  }

  export type PriceSumAggregateInputType = {
    minTon?: true
  }

  export type PriceMinAggregateInputType = {
    modelKey?: true
    minTon?: true
    updatedAt?: true
  }

  export type PriceMaxAggregateInputType = {
    modelKey?: true
    minTon?: true
    updatedAt?: true
  }

  export type PriceCountAggregateInputType = {
    modelKey?: true
    minTon?: true
    updatedAt?: true
    _all?: true
  }

  export type PriceAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Price to aggregate.
     */
    where?: PriceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Prices to fetch.
     */
    orderBy?: PriceOrderByWithRelationInput | PriceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: PriceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Prices from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Prices.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Prices
    **/
    _count?: true | PriceCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: PriceAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: PriceSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: PriceMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: PriceMaxAggregateInputType
  }

  export type GetPriceAggregateType<T extends PriceAggregateArgs> = {
        [P in keyof T & keyof AggregatePrice]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregatePrice[P]>
      : GetScalarType<T[P], AggregatePrice[P]>
  }




  export type PriceGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PriceWhereInput
    orderBy?: PriceOrderByWithAggregationInput | PriceOrderByWithAggregationInput[]
    by: PriceScalarFieldEnum[] | PriceScalarFieldEnum
    having?: PriceScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: PriceCountAggregateInputType | true
    _avg?: PriceAvgAggregateInputType
    _sum?: PriceSumAggregateInputType
    _min?: PriceMinAggregateInputType
    _max?: PriceMaxAggregateInputType
  }

  export type PriceGroupByOutputType = {
    modelKey: string
    minTon: Decimal
    updatedAt: Date
    _count: PriceCountAggregateOutputType | null
    _avg: PriceAvgAggregateOutputType | null
    _sum: PriceSumAggregateOutputType | null
    _min: PriceMinAggregateOutputType | null
    _max: PriceMaxAggregateOutputType | null
  }

  type GetPriceGroupByPayload<T extends PriceGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<PriceGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof PriceGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], PriceGroupByOutputType[P]>
            : GetScalarType<T[P], PriceGroupByOutputType[P]>
        }
      >
    >


  export type PriceSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    modelKey?: boolean
    minTon?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["price"]>

  export type PriceSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    modelKey?: boolean
    minTon?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["price"]>

  export type PriceSelectScalar = {
    modelKey?: boolean
    minTon?: boolean
    updatedAt?: boolean
  }


  export type $PricePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Price"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      modelKey: string
      minTon: Prisma.Decimal
      updatedAt: Date
    }, ExtArgs["result"]["price"]>
    composites: {}
  }

  type PriceGetPayload<S extends boolean | null | undefined | PriceDefaultArgs> = $Result.GetResult<Prisma.$PricePayload, S>

  type PriceCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<PriceFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: PriceCountAggregateInputType | true
    }

  export interface PriceDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Price'], meta: { name: 'Price' } }
    /**
     * Find zero or one Price that matches the filter.
     * @param {PriceFindUniqueArgs} args - Arguments to find a Price
     * @example
     * // Get one Price
     * const price = await prisma.price.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends PriceFindUniqueArgs>(args: SelectSubset<T, PriceFindUniqueArgs<ExtArgs>>): Prisma__PriceClient<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one Price that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {PriceFindUniqueOrThrowArgs} args - Arguments to find a Price
     * @example
     * // Get one Price
     * const price = await prisma.price.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends PriceFindUniqueOrThrowArgs>(args: SelectSubset<T, PriceFindUniqueOrThrowArgs<ExtArgs>>): Prisma__PriceClient<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first Price that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PriceFindFirstArgs} args - Arguments to find a Price
     * @example
     * // Get one Price
     * const price = await prisma.price.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends PriceFindFirstArgs>(args?: SelectSubset<T, PriceFindFirstArgs<ExtArgs>>): Prisma__PriceClient<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first Price that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PriceFindFirstOrThrowArgs} args - Arguments to find a Price
     * @example
     * // Get one Price
     * const price = await prisma.price.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends PriceFindFirstOrThrowArgs>(args?: SelectSubset<T, PriceFindFirstOrThrowArgs<ExtArgs>>): Prisma__PriceClient<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Prices that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PriceFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Prices
     * const prices = await prisma.price.findMany()
     * 
     * // Get first 10 Prices
     * const prices = await prisma.price.findMany({ take: 10 })
     * 
     * // Only select the `modelKey`
     * const priceWithModelKeyOnly = await prisma.price.findMany({ select: { modelKey: true } })
     * 
     */
    findMany<T extends PriceFindManyArgs>(args?: SelectSubset<T, PriceFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "findMany">>

    /**
     * Create a Price.
     * @param {PriceCreateArgs} args - Arguments to create a Price.
     * @example
     * // Create one Price
     * const Price = await prisma.price.create({
     *   data: {
     *     // ... data to create a Price
     *   }
     * })
     * 
     */
    create<T extends PriceCreateArgs>(args: SelectSubset<T, PriceCreateArgs<ExtArgs>>): Prisma__PriceClient<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Prices.
     * @param {PriceCreateManyArgs} args - Arguments to create many Prices.
     * @example
     * // Create many Prices
     * const price = await prisma.price.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends PriceCreateManyArgs>(args?: SelectSubset<T, PriceCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Prices and returns the data saved in the database.
     * @param {PriceCreateManyAndReturnArgs} args - Arguments to create many Prices.
     * @example
     * // Create many Prices
     * const price = await prisma.price.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Prices and only return the `modelKey`
     * const priceWithModelKeyOnly = await prisma.price.createManyAndReturn({ 
     *   select: { modelKey: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends PriceCreateManyAndReturnArgs>(args?: SelectSubset<T, PriceCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a Price.
     * @param {PriceDeleteArgs} args - Arguments to delete one Price.
     * @example
     * // Delete one Price
     * const Price = await prisma.price.delete({
     *   where: {
     *     // ... filter to delete one Price
     *   }
     * })
     * 
     */
    delete<T extends PriceDeleteArgs>(args: SelectSubset<T, PriceDeleteArgs<ExtArgs>>): Prisma__PriceClient<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one Price.
     * @param {PriceUpdateArgs} args - Arguments to update one Price.
     * @example
     * // Update one Price
     * const price = await prisma.price.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends PriceUpdateArgs>(args: SelectSubset<T, PriceUpdateArgs<ExtArgs>>): Prisma__PriceClient<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Prices.
     * @param {PriceDeleteManyArgs} args - Arguments to filter Prices to delete.
     * @example
     * // Delete a few Prices
     * const { count } = await prisma.price.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends PriceDeleteManyArgs>(args?: SelectSubset<T, PriceDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Prices.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PriceUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Prices
     * const price = await prisma.price.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends PriceUpdateManyArgs>(args: SelectSubset<T, PriceUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Price.
     * @param {PriceUpsertArgs} args - Arguments to update or create a Price.
     * @example
     * // Update or create a Price
     * const price = await prisma.price.upsert({
     *   create: {
     *     // ... data to create a Price
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Price we want to update
     *   }
     * })
     */
    upsert<T extends PriceUpsertArgs>(args: SelectSubset<T, PriceUpsertArgs<ExtArgs>>): Prisma__PriceClient<$Result.GetResult<Prisma.$PricePayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Prices.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PriceCountArgs} args - Arguments to filter Prices to count.
     * @example
     * // Count the number of Prices
     * const count = await prisma.price.count({
     *   where: {
     *     // ... the filter for the Prices we want to count
     *   }
     * })
    **/
    count<T extends PriceCountArgs>(
      args?: Subset<T, PriceCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], PriceCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Price.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PriceAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends PriceAggregateArgs>(args: Subset<T, PriceAggregateArgs>): Prisma.PrismaPromise<GetPriceAggregateType<T>>

    /**
     * Group by Price.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PriceGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends PriceGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: PriceGroupByArgs['orderBy'] }
        : { orderBy?: PriceGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, PriceGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPriceGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Price model
   */
  readonly fields: PriceFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Price.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__PriceClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Price model
   */ 
  interface PriceFieldRefs {
    readonly modelKey: FieldRef<"Price", 'String'>
    readonly minTon: FieldRef<"Price", 'Decimal'>
    readonly updatedAt: FieldRef<"Price", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Price findUnique
   */
  export type PriceFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * Filter, which Price to fetch.
     */
    where: PriceWhereUniqueInput
  }

  /**
   * Price findUniqueOrThrow
   */
  export type PriceFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * Filter, which Price to fetch.
     */
    where: PriceWhereUniqueInput
  }

  /**
   * Price findFirst
   */
  export type PriceFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * Filter, which Price to fetch.
     */
    where?: PriceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Prices to fetch.
     */
    orderBy?: PriceOrderByWithRelationInput | PriceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Prices.
     */
    cursor?: PriceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Prices from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Prices.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Prices.
     */
    distinct?: PriceScalarFieldEnum | PriceScalarFieldEnum[]
  }

  /**
   * Price findFirstOrThrow
   */
  export type PriceFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * Filter, which Price to fetch.
     */
    where?: PriceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Prices to fetch.
     */
    orderBy?: PriceOrderByWithRelationInput | PriceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Prices.
     */
    cursor?: PriceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Prices from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Prices.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Prices.
     */
    distinct?: PriceScalarFieldEnum | PriceScalarFieldEnum[]
  }

  /**
   * Price findMany
   */
  export type PriceFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * Filter, which Prices to fetch.
     */
    where?: PriceWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Prices to fetch.
     */
    orderBy?: PriceOrderByWithRelationInput | PriceOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Prices.
     */
    cursor?: PriceWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Prices from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Prices.
     */
    skip?: number
    distinct?: PriceScalarFieldEnum | PriceScalarFieldEnum[]
  }

  /**
   * Price create
   */
  export type PriceCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * The data needed to create a Price.
     */
    data: XOR<PriceCreateInput, PriceUncheckedCreateInput>
  }

  /**
   * Price createMany
   */
  export type PriceCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Prices.
     */
    data: PriceCreateManyInput | PriceCreateManyInput[]
  }

  /**
   * Price createManyAndReturn
   */
  export type PriceCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Prices.
     */
    data: PriceCreateManyInput | PriceCreateManyInput[]
  }

  /**
   * Price update
   */
  export type PriceUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * The data needed to update a Price.
     */
    data: XOR<PriceUpdateInput, PriceUncheckedUpdateInput>
    /**
     * Choose, which Price to update.
     */
    where: PriceWhereUniqueInput
  }

  /**
   * Price updateMany
   */
  export type PriceUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Prices.
     */
    data: XOR<PriceUpdateManyMutationInput, PriceUncheckedUpdateManyInput>
    /**
     * Filter which Prices to update
     */
    where?: PriceWhereInput
  }

  /**
   * Price upsert
   */
  export type PriceUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * The filter to search for the Price to update in case it exists.
     */
    where: PriceWhereUniqueInput
    /**
     * In case the Price found by the `where` argument doesn't exist, create a new Price with this data.
     */
    create: XOR<PriceCreateInput, PriceUncheckedCreateInput>
    /**
     * In case the Price was found with the provided `where` argument, update it with this data.
     */
    update: XOR<PriceUpdateInput, PriceUncheckedUpdateInput>
  }

  /**
   * Price delete
   */
  export type PriceDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
    /**
     * Filter which Price to delete.
     */
    where: PriceWhereUniqueInput
  }

  /**
   * Price deleteMany
   */
  export type PriceDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Prices to delete
     */
    where?: PriceWhereInput
  }

  /**
   * Price without action
   */
  export type PriceDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Price
     */
    select?: PriceSelect<ExtArgs> | null
  }


  /**
   * Model Transaction
   */

  export type AggregateTransaction = {
    _count: TransactionCountAggregateOutputType | null
    _avg: TransactionAvgAggregateOutputType | null
    _sum: TransactionSumAggregateOutputType | null
    _min: TransactionMinAggregateOutputType | null
    _max: TransactionMaxAggregateOutputType | null
  }

  export type TransactionAvgAggregateOutputType = {
    id: number | null
    amountTon: Decimal | null
    userId: number | null
  }

  export type TransactionSumAggregateOutputType = {
    id: number | null
    amountTon: Decimal | null
    userId: number | null
  }

  export type TransactionMinAggregateOutputType = {
    id: number | null
    kind: string | null
    amountTon: Decimal | null
    ref: string | null
    userId: number | null
    createdAt: Date | null
  }

  export type TransactionMaxAggregateOutputType = {
    id: number | null
    kind: string | null
    amountTon: Decimal | null
    ref: string | null
    userId: number | null
    createdAt: Date | null
  }

  export type TransactionCountAggregateOutputType = {
    id: number
    kind: number
    amountTon: number
    ref: number
    userId: number
    createdAt: number
    _all: number
  }


  export type TransactionAvgAggregateInputType = {
    id?: true
    amountTon?: true
    userId?: true
  }

  export type TransactionSumAggregateInputType = {
    id?: true
    amountTon?: true
    userId?: true
  }

  export type TransactionMinAggregateInputType = {
    id?: true
    kind?: true
    amountTon?: true
    ref?: true
    userId?: true
    createdAt?: true
  }

  export type TransactionMaxAggregateInputType = {
    id?: true
    kind?: true
    amountTon?: true
    ref?: true
    userId?: true
    createdAt?: true
  }

  export type TransactionCountAggregateInputType = {
    id?: true
    kind?: true
    amountTon?: true
    ref?: true
    userId?: true
    createdAt?: true
    _all?: true
  }

  export type TransactionAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Transaction to aggregate.
     */
    where?: TransactionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Transactions to fetch.
     */
    orderBy?: TransactionOrderByWithRelationInput | TransactionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TransactionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Transactions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Transactions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Transactions
    **/
    _count?: true | TransactionCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: TransactionAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: TransactionSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TransactionMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TransactionMaxAggregateInputType
  }

  export type GetTransactionAggregateType<T extends TransactionAggregateArgs> = {
        [P in keyof T & keyof AggregateTransaction]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTransaction[P]>
      : GetScalarType<T[P], AggregateTransaction[P]>
  }




  export type TransactionGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TransactionWhereInput
    orderBy?: TransactionOrderByWithAggregationInput | TransactionOrderByWithAggregationInput[]
    by: TransactionScalarFieldEnum[] | TransactionScalarFieldEnum
    having?: TransactionScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TransactionCountAggregateInputType | true
    _avg?: TransactionAvgAggregateInputType
    _sum?: TransactionSumAggregateInputType
    _min?: TransactionMinAggregateInputType
    _max?: TransactionMaxAggregateInputType
  }

  export type TransactionGroupByOutputType = {
    id: number
    kind: string
    amountTon: Decimal
    ref: string | null
    userId: number | null
    createdAt: Date
    _count: TransactionCountAggregateOutputType | null
    _avg: TransactionAvgAggregateOutputType | null
    _sum: TransactionSumAggregateOutputType | null
    _min: TransactionMinAggregateOutputType | null
    _max: TransactionMaxAggregateOutputType | null
  }

  type GetTransactionGroupByPayload<T extends TransactionGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TransactionGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TransactionGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TransactionGroupByOutputType[P]>
            : GetScalarType<T[P], TransactionGroupByOutputType[P]>
        }
      >
    >


  export type TransactionSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    kind?: boolean
    amountTon?: boolean
    ref?: boolean
    userId?: boolean
    createdAt?: boolean
    user?: boolean | Transaction$userArgs<ExtArgs>
  }, ExtArgs["result"]["transaction"]>

  export type TransactionSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    kind?: boolean
    amountTon?: boolean
    ref?: boolean
    userId?: boolean
    createdAt?: boolean
    user?: boolean | Transaction$userArgs<ExtArgs>
  }, ExtArgs["result"]["transaction"]>

  export type TransactionSelectScalar = {
    id?: boolean
    kind?: boolean
    amountTon?: boolean
    ref?: boolean
    userId?: boolean
    createdAt?: boolean
  }

  export type TransactionInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | Transaction$userArgs<ExtArgs>
  }
  export type TransactionIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | Transaction$userArgs<ExtArgs>
  }

  export type $TransactionPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Transaction"
    objects: {
      user: Prisma.$UserPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      kind: string
      amountTon: Prisma.Decimal
      ref: string | null
      userId: number | null
      createdAt: Date
    }, ExtArgs["result"]["transaction"]>
    composites: {}
  }

  type TransactionGetPayload<S extends boolean | null | undefined | TransactionDefaultArgs> = $Result.GetResult<Prisma.$TransactionPayload, S>

  type TransactionCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<TransactionFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: TransactionCountAggregateInputType | true
    }

  export interface TransactionDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Transaction'], meta: { name: 'Transaction' } }
    /**
     * Find zero or one Transaction that matches the filter.
     * @param {TransactionFindUniqueArgs} args - Arguments to find a Transaction
     * @example
     * // Get one Transaction
     * const transaction = await prisma.transaction.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TransactionFindUniqueArgs>(args: SelectSubset<T, TransactionFindUniqueArgs<ExtArgs>>): Prisma__TransactionClient<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one Transaction that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {TransactionFindUniqueOrThrowArgs} args - Arguments to find a Transaction
     * @example
     * // Get one Transaction
     * const transaction = await prisma.transaction.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TransactionFindUniqueOrThrowArgs>(args: SelectSubset<T, TransactionFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TransactionClient<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first Transaction that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TransactionFindFirstArgs} args - Arguments to find a Transaction
     * @example
     * // Get one Transaction
     * const transaction = await prisma.transaction.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TransactionFindFirstArgs>(args?: SelectSubset<T, TransactionFindFirstArgs<ExtArgs>>): Prisma__TransactionClient<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first Transaction that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TransactionFindFirstOrThrowArgs} args - Arguments to find a Transaction
     * @example
     * // Get one Transaction
     * const transaction = await prisma.transaction.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TransactionFindFirstOrThrowArgs>(args?: SelectSubset<T, TransactionFindFirstOrThrowArgs<ExtArgs>>): Prisma__TransactionClient<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Transactions that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TransactionFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Transactions
     * const transactions = await prisma.transaction.findMany()
     * 
     * // Get first 10 Transactions
     * const transactions = await prisma.transaction.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const transactionWithIdOnly = await prisma.transaction.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends TransactionFindManyArgs>(args?: SelectSubset<T, TransactionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a Transaction.
     * @param {TransactionCreateArgs} args - Arguments to create a Transaction.
     * @example
     * // Create one Transaction
     * const Transaction = await prisma.transaction.create({
     *   data: {
     *     // ... data to create a Transaction
     *   }
     * })
     * 
     */
    create<T extends TransactionCreateArgs>(args: SelectSubset<T, TransactionCreateArgs<ExtArgs>>): Prisma__TransactionClient<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Transactions.
     * @param {TransactionCreateManyArgs} args - Arguments to create many Transactions.
     * @example
     * // Create many Transactions
     * const transaction = await prisma.transaction.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TransactionCreateManyArgs>(args?: SelectSubset<T, TransactionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Transactions and returns the data saved in the database.
     * @param {TransactionCreateManyAndReturnArgs} args - Arguments to create many Transactions.
     * @example
     * // Create many Transactions
     * const transaction = await prisma.transaction.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Transactions and only return the `id`
     * const transactionWithIdOnly = await prisma.transaction.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TransactionCreateManyAndReturnArgs>(args?: SelectSubset<T, TransactionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a Transaction.
     * @param {TransactionDeleteArgs} args - Arguments to delete one Transaction.
     * @example
     * // Delete one Transaction
     * const Transaction = await prisma.transaction.delete({
     *   where: {
     *     // ... filter to delete one Transaction
     *   }
     * })
     * 
     */
    delete<T extends TransactionDeleteArgs>(args: SelectSubset<T, TransactionDeleteArgs<ExtArgs>>): Prisma__TransactionClient<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one Transaction.
     * @param {TransactionUpdateArgs} args - Arguments to update one Transaction.
     * @example
     * // Update one Transaction
     * const transaction = await prisma.transaction.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TransactionUpdateArgs>(args: SelectSubset<T, TransactionUpdateArgs<ExtArgs>>): Prisma__TransactionClient<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Transactions.
     * @param {TransactionDeleteManyArgs} args - Arguments to filter Transactions to delete.
     * @example
     * // Delete a few Transactions
     * const { count } = await prisma.transaction.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TransactionDeleteManyArgs>(args?: SelectSubset<T, TransactionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Transactions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TransactionUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Transactions
     * const transaction = await prisma.transaction.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TransactionUpdateManyArgs>(args: SelectSubset<T, TransactionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Transaction.
     * @param {TransactionUpsertArgs} args - Arguments to update or create a Transaction.
     * @example
     * // Update or create a Transaction
     * const transaction = await prisma.transaction.upsert({
     *   create: {
     *     // ... data to create a Transaction
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Transaction we want to update
     *   }
     * })
     */
    upsert<T extends TransactionUpsertArgs>(args: SelectSubset<T, TransactionUpsertArgs<ExtArgs>>): Prisma__TransactionClient<$Result.GetResult<Prisma.$TransactionPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Transactions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TransactionCountArgs} args - Arguments to filter Transactions to count.
     * @example
     * // Count the number of Transactions
     * const count = await prisma.transaction.count({
     *   where: {
     *     // ... the filter for the Transactions we want to count
     *   }
     * })
    **/
    count<T extends TransactionCountArgs>(
      args?: Subset<T, TransactionCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TransactionCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Transaction.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TransactionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TransactionAggregateArgs>(args: Subset<T, TransactionAggregateArgs>): Prisma.PrismaPromise<GetTransactionAggregateType<T>>

    /**
     * Group by Transaction.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TransactionGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TransactionGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TransactionGroupByArgs['orderBy'] }
        : { orderBy?: TransactionGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TransactionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTransactionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Transaction model
   */
  readonly fields: TransactionFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Transaction.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TransactionClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends Transaction$userArgs<ExtArgs> = {}>(args?: Subset<T, Transaction$userArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | null, null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Transaction model
   */ 
  interface TransactionFieldRefs {
    readonly id: FieldRef<"Transaction", 'Int'>
    readonly kind: FieldRef<"Transaction", 'String'>
    readonly amountTon: FieldRef<"Transaction", 'Decimal'>
    readonly ref: FieldRef<"Transaction", 'String'>
    readonly userId: FieldRef<"Transaction", 'Int'>
    readonly createdAt: FieldRef<"Transaction", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Transaction findUnique
   */
  export type TransactionFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * Filter, which Transaction to fetch.
     */
    where: TransactionWhereUniqueInput
  }

  /**
   * Transaction findUniqueOrThrow
   */
  export type TransactionFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * Filter, which Transaction to fetch.
     */
    where: TransactionWhereUniqueInput
  }

  /**
   * Transaction findFirst
   */
  export type TransactionFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * Filter, which Transaction to fetch.
     */
    where?: TransactionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Transactions to fetch.
     */
    orderBy?: TransactionOrderByWithRelationInput | TransactionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Transactions.
     */
    cursor?: TransactionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Transactions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Transactions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Transactions.
     */
    distinct?: TransactionScalarFieldEnum | TransactionScalarFieldEnum[]
  }

  /**
   * Transaction findFirstOrThrow
   */
  export type TransactionFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * Filter, which Transaction to fetch.
     */
    where?: TransactionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Transactions to fetch.
     */
    orderBy?: TransactionOrderByWithRelationInput | TransactionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Transactions.
     */
    cursor?: TransactionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Transactions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Transactions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Transactions.
     */
    distinct?: TransactionScalarFieldEnum | TransactionScalarFieldEnum[]
  }

  /**
   * Transaction findMany
   */
  export type TransactionFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * Filter, which Transactions to fetch.
     */
    where?: TransactionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Transactions to fetch.
     */
    orderBy?: TransactionOrderByWithRelationInput | TransactionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Transactions.
     */
    cursor?: TransactionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Transactions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Transactions.
     */
    skip?: number
    distinct?: TransactionScalarFieldEnum | TransactionScalarFieldEnum[]
  }

  /**
   * Transaction create
   */
  export type TransactionCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * The data needed to create a Transaction.
     */
    data: XOR<TransactionCreateInput, TransactionUncheckedCreateInput>
  }

  /**
   * Transaction createMany
   */
  export type TransactionCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Transactions.
     */
    data: TransactionCreateManyInput | TransactionCreateManyInput[]
  }

  /**
   * Transaction createManyAndReturn
   */
  export type TransactionCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Transactions.
     */
    data: TransactionCreateManyInput | TransactionCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Transaction update
   */
  export type TransactionUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * The data needed to update a Transaction.
     */
    data: XOR<TransactionUpdateInput, TransactionUncheckedUpdateInput>
    /**
     * Choose, which Transaction to update.
     */
    where: TransactionWhereUniqueInput
  }

  /**
   * Transaction updateMany
   */
  export type TransactionUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Transactions.
     */
    data: XOR<TransactionUpdateManyMutationInput, TransactionUncheckedUpdateManyInput>
    /**
     * Filter which Transactions to update
     */
    where?: TransactionWhereInput
  }

  /**
   * Transaction upsert
   */
  export type TransactionUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * The filter to search for the Transaction to update in case it exists.
     */
    where: TransactionWhereUniqueInput
    /**
     * In case the Transaction found by the `where` argument doesn't exist, create a new Transaction with this data.
     */
    create: XOR<TransactionCreateInput, TransactionUncheckedCreateInput>
    /**
     * In case the Transaction was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TransactionUpdateInput, TransactionUncheckedUpdateInput>
  }

  /**
   * Transaction delete
   */
  export type TransactionDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
    /**
     * Filter which Transaction to delete.
     */
    where: TransactionWhereUniqueInput
  }

  /**
   * Transaction deleteMany
   */
  export type TransactionDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Transactions to delete
     */
    where?: TransactionWhereInput
  }

  /**
   * Transaction.user
   */
  export type Transaction$userArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    where?: UserWhereInput
  }

  /**
   * Transaction without action
   */
  export type TransactionDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Transaction
     */
    select?: TransactionSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TransactionInclude<ExtArgs> | null
  }


  /**
   * Model CoinflipRoom
   */

  export type AggregateCoinflipRoom = {
    _count: CoinflipRoomCountAggregateOutputType | null
    _avg: CoinflipRoomAvgAggregateOutputType | null
    _sum: CoinflipRoomSumAggregateOutputType | null
    _min: CoinflipRoomMinAggregateOutputType | null
    _max: CoinflipRoomMaxAggregateOutputType | null
  }

  export type CoinflipRoomAvgAggregateOutputType = {
    id: number | null
    stakeTon: Decimal | null
    playerAId: number | null
    playerBId: number | null
    winnerId: number | null
  }

  export type CoinflipRoomSumAggregateOutputType = {
    id: number | null
    stakeTon: Decimal | null
    playerAId: number | null
    playerBId: number | null
    winnerId: number | null
  }

  export type CoinflipRoomMinAggregateOutputType = {
    id: number | null
    stakeTon: Decimal | null
    playerAId: number | null
    playerBId: number | null
    seedA: string | null
    seedB: string | null
    winnerId: number | null
    status: string | null
    createdAt: Date | null
    finishedAt: Date | null
  }

  export type CoinflipRoomMaxAggregateOutputType = {
    id: number | null
    stakeTon: Decimal | null
    playerAId: number | null
    playerBId: number | null
    seedA: string | null
    seedB: string | null
    winnerId: number | null
    status: string | null
    createdAt: Date | null
    finishedAt: Date | null
  }

  export type CoinflipRoomCountAggregateOutputType = {
    id: number
    stakeTon: number
    playerAId: number
    playerBId: number
    seedA: number
    seedB: number
    winnerId: number
    status: number
    createdAt: number
    finishedAt: number
    _all: number
  }


  export type CoinflipRoomAvgAggregateInputType = {
    id?: true
    stakeTon?: true
    playerAId?: true
    playerBId?: true
    winnerId?: true
  }

  export type CoinflipRoomSumAggregateInputType = {
    id?: true
    stakeTon?: true
    playerAId?: true
    playerBId?: true
    winnerId?: true
  }

  export type CoinflipRoomMinAggregateInputType = {
    id?: true
    stakeTon?: true
    playerAId?: true
    playerBId?: true
    seedA?: true
    seedB?: true
    winnerId?: true
    status?: true
    createdAt?: true
    finishedAt?: true
  }

  export type CoinflipRoomMaxAggregateInputType = {
    id?: true
    stakeTon?: true
    playerAId?: true
    playerBId?: true
    seedA?: true
    seedB?: true
    winnerId?: true
    status?: true
    createdAt?: true
    finishedAt?: true
  }

  export type CoinflipRoomCountAggregateInputType = {
    id?: true
    stakeTon?: true
    playerAId?: true
    playerBId?: true
    seedA?: true
    seedB?: true
    winnerId?: true
    status?: true
    createdAt?: true
    finishedAt?: true
    _all?: true
  }

  export type CoinflipRoomAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CoinflipRoom to aggregate.
     */
    where?: CoinflipRoomWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CoinflipRooms to fetch.
     */
    orderBy?: CoinflipRoomOrderByWithRelationInput | CoinflipRoomOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CoinflipRoomWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CoinflipRooms from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CoinflipRooms.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned CoinflipRooms
    **/
    _count?: true | CoinflipRoomCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: CoinflipRoomAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: CoinflipRoomSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CoinflipRoomMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CoinflipRoomMaxAggregateInputType
  }

  export type GetCoinflipRoomAggregateType<T extends CoinflipRoomAggregateArgs> = {
        [P in keyof T & keyof AggregateCoinflipRoom]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateCoinflipRoom[P]>
      : GetScalarType<T[P], AggregateCoinflipRoom[P]>
  }




  export type CoinflipRoomGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CoinflipRoomWhereInput
    orderBy?: CoinflipRoomOrderByWithAggregationInput | CoinflipRoomOrderByWithAggregationInput[]
    by: CoinflipRoomScalarFieldEnum[] | CoinflipRoomScalarFieldEnum
    having?: CoinflipRoomScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CoinflipRoomCountAggregateInputType | true
    _avg?: CoinflipRoomAvgAggregateInputType
    _sum?: CoinflipRoomSumAggregateInputType
    _min?: CoinflipRoomMinAggregateInputType
    _max?: CoinflipRoomMaxAggregateInputType
  }

  export type CoinflipRoomGroupByOutputType = {
    id: number
    stakeTon: Decimal
    playerAId: number
    playerBId: number | null
    seedA: string | null
    seedB: string | null
    winnerId: number | null
    status: string
    createdAt: Date
    finishedAt: Date | null
    _count: CoinflipRoomCountAggregateOutputType | null
    _avg: CoinflipRoomAvgAggregateOutputType | null
    _sum: CoinflipRoomSumAggregateOutputType | null
    _min: CoinflipRoomMinAggregateOutputType | null
    _max: CoinflipRoomMaxAggregateOutputType | null
  }

  type GetCoinflipRoomGroupByPayload<T extends CoinflipRoomGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CoinflipRoomGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CoinflipRoomGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CoinflipRoomGroupByOutputType[P]>
            : GetScalarType<T[P], CoinflipRoomGroupByOutputType[P]>
        }
      >
    >


  export type CoinflipRoomSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    stakeTon?: boolean
    playerAId?: boolean
    playerBId?: boolean
    seedA?: boolean
    seedB?: boolean
    winnerId?: boolean
    status?: boolean
    createdAt?: boolean
    finishedAt?: boolean
    playerA?: boolean | UserDefaultArgs<ExtArgs>
    playerB?: boolean | CoinflipRoom$playerBArgs<ExtArgs>
  }, ExtArgs["result"]["coinflipRoom"]>

  export type CoinflipRoomSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    stakeTon?: boolean
    playerAId?: boolean
    playerBId?: boolean
    seedA?: boolean
    seedB?: boolean
    winnerId?: boolean
    status?: boolean
    createdAt?: boolean
    finishedAt?: boolean
    playerA?: boolean | UserDefaultArgs<ExtArgs>
    playerB?: boolean | CoinflipRoom$playerBArgs<ExtArgs>
  }, ExtArgs["result"]["coinflipRoom"]>

  export type CoinflipRoomSelectScalar = {
    id?: boolean
    stakeTon?: boolean
    playerAId?: boolean
    playerBId?: boolean
    seedA?: boolean
    seedB?: boolean
    winnerId?: boolean
    status?: boolean
    createdAt?: boolean
    finishedAt?: boolean
  }

  export type CoinflipRoomInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    playerA?: boolean | UserDefaultArgs<ExtArgs>
    playerB?: boolean | CoinflipRoom$playerBArgs<ExtArgs>
  }
  export type CoinflipRoomIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    playerA?: boolean | UserDefaultArgs<ExtArgs>
    playerB?: boolean | CoinflipRoom$playerBArgs<ExtArgs>
  }

  export type $CoinflipRoomPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "CoinflipRoom"
    objects: {
      playerA: Prisma.$UserPayload<ExtArgs>
      playerB: Prisma.$UserPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      stakeTon: Prisma.Decimal
      playerAId: number
      playerBId: number | null
      seedA: string | null
      seedB: string | null
      winnerId: number | null
      status: string
      createdAt: Date
      finishedAt: Date | null
    }, ExtArgs["result"]["coinflipRoom"]>
    composites: {}
  }

  type CoinflipRoomGetPayload<S extends boolean | null | undefined | CoinflipRoomDefaultArgs> = $Result.GetResult<Prisma.$CoinflipRoomPayload, S>

  type CoinflipRoomCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<CoinflipRoomFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: CoinflipRoomCountAggregateInputType | true
    }

  export interface CoinflipRoomDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['CoinflipRoom'], meta: { name: 'CoinflipRoom' } }
    /**
     * Find zero or one CoinflipRoom that matches the filter.
     * @param {CoinflipRoomFindUniqueArgs} args - Arguments to find a CoinflipRoom
     * @example
     * // Get one CoinflipRoom
     * const coinflipRoom = await prisma.coinflipRoom.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CoinflipRoomFindUniqueArgs>(args: SelectSubset<T, CoinflipRoomFindUniqueArgs<ExtArgs>>): Prisma__CoinflipRoomClient<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one CoinflipRoom that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {CoinflipRoomFindUniqueOrThrowArgs} args - Arguments to find a CoinflipRoom
     * @example
     * // Get one CoinflipRoom
     * const coinflipRoom = await prisma.coinflipRoom.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CoinflipRoomFindUniqueOrThrowArgs>(args: SelectSubset<T, CoinflipRoomFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CoinflipRoomClient<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first CoinflipRoom that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CoinflipRoomFindFirstArgs} args - Arguments to find a CoinflipRoom
     * @example
     * // Get one CoinflipRoom
     * const coinflipRoom = await prisma.coinflipRoom.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CoinflipRoomFindFirstArgs>(args?: SelectSubset<T, CoinflipRoomFindFirstArgs<ExtArgs>>): Prisma__CoinflipRoomClient<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first CoinflipRoom that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CoinflipRoomFindFirstOrThrowArgs} args - Arguments to find a CoinflipRoom
     * @example
     * // Get one CoinflipRoom
     * const coinflipRoom = await prisma.coinflipRoom.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CoinflipRoomFindFirstOrThrowArgs>(args?: SelectSubset<T, CoinflipRoomFindFirstOrThrowArgs<ExtArgs>>): Prisma__CoinflipRoomClient<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more CoinflipRooms that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CoinflipRoomFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all CoinflipRooms
     * const coinflipRooms = await prisma.coinflipRoom.findMany()
     * 
     * // Get first 10 CoinflipRooms
     * const coinflipRooms = await prisma.coinflipRoom.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const coinflipRoomWithIdOnly = await prisma.coinflipRoom.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CoinflipRoomFindManyArgs>(args?: SelectSubset<T, CoinflipRoomFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a CoinflipRoom.
     * @param {CoinflipRoomCreateArgs} args - Arguments to create a CoinflipRoom.
     * @example
     * // Create one CoinflipRoom
     * const CoinflipRoom = await prisma.coinflipRoom.create({
     *   data: {
     *     // ... data to create a CoinflipRoom
     *   }
     * })
     * 
     */
    create<T extends CoinflipRoomCreateArgs>(args: SelectSubset<T, CoinflipRoomCreateArgs<ExtArgs>>): Prisma__CoinflipRoomClient<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many CoinflipRooms.
     * @param {CoinflipRoomCreateManyArgs} args - Arguments to create many CoinflipRooms.
     * @example
     * // Create many CoinflipRooms
     * const coinflipRoom = await prisma.coinflipRoom.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CoinflipRoomCreateManyArgs>(args?: SelectSubset<T, CoinflipRoomCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many CoinflipRooms and returns the data saved in the database.
     * @param {CoinflipRoomCreateManyAndReturnArgs} args - Arguments to create many CoinflipRooms.
     * @example
     * // Create many CoinflipRooms
     * const coinflipRoom = await prisma.coinflipRoom.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many CoinflipRooms and only return the `id`
     * const coinflipRoomWithIdOnly = await prisma.coinflipRoom.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends CoinflipRoomCreateManyAndReturnArgs>(args?: SelectSubset<T, CoinflipRoomCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a CoinflipRoom.
     * @param {CoinflipRoomDeleteArgs} args - Arguments to delete one CoinflipRoom.
     * @example
     * // Delete one CoinflipRoom
     * const CoinflipRoom = await prisma.coinflipRoom.delete({
     *   where: {
     *     // ... filter to delete one CoinflipRoom
     *   }
     * })
     * 
     */
    delete<T extends CoinflipRoomDeleteArgs>(args: SelectSubset<T, CoinflipRoomDeleteArgs<ExtArgs>>): Prisma__CoinflipRoomClient<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one CoinflipRoom.
     * @param {CoinflipRoomUpdateArgs} args - Arguments to update one CoinflipRoom.
     * @example
     * // Update one CoinflipRoom
     * const coinflipRoom = await prisma.coinflipRoom.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CoinflipRoomUpdateArgs>(args: SelectSubset<T, CoinflipRoomUpdateArgs<ExtArgs>>): Prisma__CoinflipRoomClient<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more CoinflipRooms.
     * @param {CoinflipRoomDeleteManyArgs} args - Arguments to filter CoinflipRooms to delete.
     * @example
     * // Delete a few CoinflipRooms
     * const { count } = await prisma.coinflipRoom.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CoinflipRoomDeleteManyArgs>(args?: SelectSubset<T, CoinflipRoomDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more CoinflipRooms.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CoinflipRoomUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many CoinflipRooms
     * const coinflipRoom = await prisma.coinflipRoom.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CoinflipRoomUpdateManyArgs>(args: SelectSubset<T, CoinflipRoomUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one CoinflipRoom.
     * @param {CoinflipRoomUpsertArgs} args - Arguments to update or create a CoinflipRoom.
     * @example
     * // Update or create a CoinflipRoom
     * const coinflipRoom = await prisma.coinflipRoom.upsert({
     *   create: {
     *     // ... data to create a CoinflipRoom
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the CoinflipRoom we want to update
     *   }
     * })
     */
    upsert<T extends CoinflipRoomUpsertArgs>(args: SelectSubset<T, CoinflipRoomUpsertArgs<ExtArgs>>): Prisma__CoinflipRoomClient<$Result.GetResult<Prisma.$CoinflipRoomPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of CoinflipRooms.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CoinflipRoomCountArgs} args - Arguments to filter CoinflipRooms to count.
     * @example
     * // Count the number of CoinflipRooms
     * const count = await prisma.coinflipRoom.count({
     *   where: {
     *     // ... the filter for the CoinflipRooms we want to count
     *   }
     * })
    **/
    count<T extends CoinflipRoomCountArgs>(
      args?: Subset<T, CoinflipRoomCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CoinflipRoomCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a CoinflipRoom.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CoinflipRoomAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CoinflipRoomAggregateArgs>(args: Subset<T, CoinflipRoomAggregateArgs>): Prisma.PrismaPromise<GetCoinflipRoomAggregateType<T>>

    /**
     * Group by CoinflipRoom.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CoinflipRoomGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CoinflipRoomGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CoinflipRoomGroupByArgs['orderBy'] }
        : { orderBy?: CoinflipRoomGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CoinflipRoomGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCoinflipRoomGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the CoinflipRoom model
   */
  readonly fields: CoinflipRoomFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for CoinflipRoom.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CoinflipRoomClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    playerA<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    playerB<T extends CoinflipRoom$playerBArgs<ExtArgs> = {}>(args?: Subset<T, CoinflipRoom$playerBArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | null, null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the CoinflipRoom model
   */ 
  interface CoinflipRoomFieldRefs {
    readonly id: FieldRef<"CoinflipRoom", 'Int'>
    readonly stakeTon: FieldRef<"CoinflipRoom", 'Decimal'>
    readonly playerAId: FieldRef<"CoinflipRoom", 'Int'>
    readonly playerBId: FieldRef<"CoinflipRoom", 'Int'>
    readonly seedA: FieldRef<"CoinflipRoom", 'String'>
    readonly seedB: FieldRef<"CoinflipRoom", 'String'>
    readonly winnerId: FieldRef<"CoinflipRoom", 'Int'>
    readonly status: FieldRef<"CoinflipRoom", 'String'>
    readonly createdAt: FieldRef<"CoinflipRoom", 'DateTime'>
    readonly finishedAt: FieldRef<"CoinflipRoom", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * CoinflipRoom findUnique
   */
  export type CoinflipRoomFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * Filter, which CoinflipRoom to fetch.
     */
    where: CoinflipRoomWhereUniqueInput
  }

  /**
   * CoinflipRoom findUniqueOrThrow
   */
  export type CoinflipRoomFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * Filter, which CoinflipRoom to fetch.
     */
    where: CoinflipRoomWhereUniqueInput
  }

  /**
   * CoinflipRoom findFirst
   */
  export type CoinflipRoomFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * Filter, which CoinflipRoom to fetch.
     */
    where?: CoinflipRoomWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CoinflipRooms to fetch.
     */
    orderBy?: CoinflipRoomOrderByWithRelationInput | CoinflipRoomOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CoinflipRooms.
     */
    cursor?: CoinflipRoomWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CoinflipRooms from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CoinflipRooms.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CoinflipRooms.
     */
    distinct?: CoinflipRoomScalarFieldEnum | CoinflipRoomScalarFieldEnum[]
  }

  /**
   * CoinflipRoom findFirstOrThrow
   */
  export type CoinflipRoomFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * Filter, which CoinflipRoom to fetch.
     */
    where?: CoinflipRoomWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CoinflipRooms to fetch.
     */
    orderBy?: CoinflipRoomOrderByWithRelationInput | CoinflipRoomOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CoinflipRooms.
     */
    cursor?: CoinflipRoomWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CoinflipRooms from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CoinflipRooms.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CoinflipRooms.
     */
    distinct?: CoinflipRoomScalarFieldEnum | CoinflipRoomScalarFieldEnum[]
  }

  /**
   * CoinflipRoom findMany
   */
  export type CoinflipRoomFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * Filter, which CoinflipRooms to fetch.
     */
    where?: CoinflipRoomWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CoinflipRooms to fetch.
     */
    orderBy?: CoinflipRoomOrderByWithRelationInput | CoinflipRoomOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing CoinflipRooms.
     */
    cursor?: CoinflipRoomWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CoinflipRooms from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CoinflipRooms.
     */
    skip?: number
    distinct?: CoinflipRoomScalarFieldEnum | CoinflipRoomScalarFieldEnum[]
  }

  /**
   * CoinflipRoom create
   */
  export type CoinflipRoomCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * The data needed to create a CoinflipRoom.
     */
    data: XOR<CoinflipRoomCreateInput, CoinflipRoomUncheckedCreateInput>
  }

  /**
   * CoinflipRoom createMany
   */
  export type CoinflipRoomCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many CoinflipRooms.
     */
    data: CoinflipRoomCreateManyInput | CoinflipRoomCreateManyInput[]
  }

  /**
   * CoinflipRoom createManyAndReturn
   */
  export type CoinflipRoomCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many CoinflipRooms.
     */
    data: CoinflipRoomCreateManyInput | CoinflipRoomCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * CoinflipRoom update
   */
  export type CoinflipRoomUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * The data needed to update a CoinflipRoom.
     */
    data: XOR<CoinflipRoomUpdateInput, CoinflipRoomUncheckedUpdateInput>
    /**
     * Choose, which CoinflipRoom to update.
     */
    where: CoinflipRoomWhereUniqueInput
  }

  /**
   * CoinflipRoom updateMany
   */
  export type CoinflipRoomUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update CoinflipRooms.
     */
    data: XOR<CoinflipRoomUpdateManyMutationInput, CoinflipRoomUncheckedUpdateManyInput>
    /**
     * Filter which CoinflipRooms to update
     */
    where?: CoinflipRoomWhereInput
  }

  /**
   * CoinflipRoom upsert
   */
  export type CoinflipRoomUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * The filter to search for the CoinflipRoom to update in case it exists.
     */
    where: CoinflipRoomWhereUniqueInput
    /**
     * In case the CoinflipRoom found by the `where` argument doesn't exist, create a new CoinflipRoom with this data.
     */
    create: XOR<CoinflipRoomCreateInput, CoinflipRoomUncheckedCreateInput>
    /**
     * In case the CoinflipRoom was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CoinflipRoomUpdateInput, CoinflipRoomUncheckedUpdateInput>
  }

  /**
   * CoinflipRoom delete
   */
  export type CoinflipRoomDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
    /**
     * Filter which CoinflipRoom to delete.
     */
    where: CoinflipRoomWhereUniqueInput
  }

  /**
   * CoinflipRoom deleteMany
   */
  export type CoinflipRoomDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CoinflipRooms to delete
     */
    where?: CoinflipRoomWhereInput
  }

  /**
   * CoinflipRoom.playerB
   */
  export type CoinflipRoom$playerBArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    where?: UserWhereInput
  }

  /**
   * CoinflipRoom without action
   */
  export type CoinflipRoomDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CoinflipRoom
     */
    select?: CoinflipRoomSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CoinflipRoomInclude<ExtArgs> | null
  }


  /**
   * Model CrashRound
   */

  export type AggregateCrashRound = {
    _count: CrashRoundCountAggregateOutputType | null
    _avg: CrashRoundAvgAggregateOutputType | null
    _sum: CrashRoundSumAggregateOutputType | null
    _min: CrashRoundMinAggregateOutputType | null
    _max: CrashRoundMaxAggregateOutputType | null
  }

  export type CrashRoundAvgAggregateOutputType = {
    id: number | null
    multiplier: Decimal | null
  }

  export type CrashRoundSumAggregateOutputType = {
    id: number | null
    multiplier: Decimal | null
  }

  export type CrashRoundMinAggregateOutputType = {
    id: number | null
    multiplier: Decimal | null
    seed: string | null
    status: string | null
    createdAt: Date | null
    crashedAt: Date | null
  }

  export type CrashRoundMaxAggregateOutputType = {
    id: number | null
    multiplier: Decimal | null
    seed: string | null
    status: string | null
    createdAt: Date | null
    crashedAt: Date | null
  }

  export type CrashRoundCountAggregateOutputType = {
    id: number
    multiplier: number
    seed: number
    status: number
    createdAt: number
    crashedAt: number
    _all: number
  }


  export type CrashRoundAvgAggregateInputType = {
    id?: true
    multiplier?: true
  }

  export type CrashRoundSumAggregateInputType = {
    id?: true
    multiplier?: true
  }

  export type CrashRoundMinAggregateInputType = {
    id?: true
    multiplier?: true
    seed?: true
    status?: true
    createdAt?: true
    crashedAt?: true
  }

  export type CrashRoundMaxAggregateInputType = {
    id?: true
    multiplier?: true
    seed?: true
    status?: true
    createdAt?: true
    crashedAt?: true
  }

  export type CrashRoundCountAggregateInputType = {
    id?: true
    multiplier?: true
    seed?: true
    status?: true
    createdAt?: true
    crashedAt?: true
    _all?: true
  }

  export type CrashRoundAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CrashRound to aggregate.
     */
    where?: CrashRoundWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrashRounds to fetch.
     */
    orderBy?: CrashRoundOrderByWithRelationInput | CrashRoundOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CrashRoundWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrashRounds from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrashRounds.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned CrashRounds
    **/
    _count?: true | CrashRoundCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: CrashRoundAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: CrashRoundSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CrashRoundMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CrashRoundMaxAggregateInputType
  }

  export type GetCrashRoundAggregateType<T extends CrashRoundAggregateArgs> = {
        [P in keyof T & keyof AggregateCrashRound]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateCrashRound[P]>
      : GetScalarType<T[P], AggregateCrashRound[P]>
  }




  export type CrashRoundGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CrashRoundWhereInput
    orderBy?: CrashRoundOrderByWithAggregationInput | CrashRoundOrderByWithAggregationInput[]
    by: CrashRoundScalarFieldEnum[] | CrashRoundScalarFieldEnum
    having?: CrashRoundScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CrashRoundCountAggregateInputType | true
    _avg?: CrashRoundAvgAggregateInputType
    _sum?: CrashRoundSumAggregateInputType
    _min?: CrashRoundMinAggregateInputType
    _max?: CrashRoundMaxAggregateInputType
  }

  export type CrashRoundGroupByOutputType = {
    id: number
    multiplier: Decimal
    seed: string
    status: string
    createdAt: Date
    crashedAt: Date | null
    _count: CrashRoundCountAggregateOutputType | null
    _avg: CrashRoundAvgAggregateOutputType | null
    _sum: CrashRoundSumAggregateOutputType | null
    _min: CrashRoundMinAggregateOutputType | null
    _max: CrashRoundMaxAggregateOutputType | null
  }

  type GetCrashRoundGroupByPayload<T extends CrashRoundGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CrashRoundGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CrashRoundGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CrashRoundGroupByOutputType[P]>
            : GetScalarType<T[P], CrashRoundGroupByOutputType[P]>
        }
      >
    >


  export type CrashRoundSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    multiplier?: boolean
    seed?: boolean
    status?: boolean
    createdAt?: boolean
    crashedAt?: boolean
    bets?: boolean | CrashRound$betsArgs<ExtArgs>
    _count?: boolean | CrashRoundCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["crashRound"]>

  export type CrashRoundSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    multiplier?: boolean
    seed?: boolean
    status?: boolean
    createdAt?: boolean
    crashedAt?: boolean
  }, ExtArgs["result"]["crashRound"]>

  export type CrashRoundSelectScalar = {
    id?: boolean
    multiplier?: boolean
    seed?: boolean
    status?: boolean
    createdAt?: boolean
    crashedAt?: boolean
  }

  export type CrashRoundInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    bets?: boolean | CrashRound$betsArgs<ExtArgs>
    _count?: boolean | CrashRoundCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type CrashRoundIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $CrashRoundPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "CrashRound"
    objects: {
      bets: Prisma.$CrashBetPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      multiplier: Prisma.Decimal
      seed: string
      status: string
      createdAt: Date
      crashedAt: Date | null
    }, ExtArgs["result"]["crashRound"]>
    composites: {}
  }

  type CrashRoundGetPayload<S extends boolean | null | undefined | CrashRoundDefaultArgs> = $Result.GetResult<Prisma.$CrashRoundPayload, S>

  type CrashRoundCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<CrashRoundFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: CrashRoundCountAggregateInputType | true
    }

  export interface CrashRoundDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['CrashRound'], meta: { name: 'CrashRound' } }
    /**
     * Find zero or one CrashRound that matches the filter.
     * @param {CrashRoundFindUniqueArgs} args - Arguments to find a CrashRound
     * @example
     * // Get one CrashRound
     * const crashRound = await prisma.crashRound.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CrashRoundFindUniqueArgs>(args: SelectSubset<T, CrashRoundFindUniqueArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one CrashRound that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {CrashRoundFindUniqueOrThrowArgs} args - Arguments to find a CrashRound
     * @example
     * // Get one CrashRound
     * const crashRound = await prisma.crashRound.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CrashRoundFindUniqueOrThrowArgs>(args: SelectSubset<T, CrashRoundFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first CrashRound that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashRoundFindFirstArgs} args - Arguments to find a CrashRound
     * @example
     * // Get one CrashRound
     * const crashRound = await prisma.crashRound.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CrashRoundFindFirstArgs>(args?: SelectSubset<T, CrashRoundFindFirstArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first CrashRound that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashRoundFindFirstOrThrowArgs} args - Arguments to find a CrashRound
     * @example
     * // Get one CrashRound
     * const crashRound = await prisma.crashRound.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CrashRoundFindFirstOrThrowArgs>(args?: SelectSubset<T, CrashRoundFindFirstOrThrowArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more CrashRounds that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashRoundFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all CrashRounds
     * const crashRounds = await prisma.crashRound.findMany()
     * 
     * // Get first 10 CrashRounds
     * const crashRounds = await prisma.crashRound.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const crashRoundWithIdOnly = await prisma.crashRound.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CrashRoundFindManyArgs>(args?: SelectSubset<T, CrashRoundFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a CrashRound.
     * @param {CrashRoundCreateArgs} args - Arguments to create a CrashRound.
     * @example
     * // Create one CrashRound
     * const CrashRound = await prisma.crashRound.create({
     *   data: {
     *     // ... data to create a CrashRound
     *   }
     * })
     * 
     */
    create<T extends CrashRoundCreateArgs>(args: SelectSubset<T, CrashRoundCreateArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many CrashRounds.
     * @param {CrashRoundCreateManyArgs} args - Arguments to create many CrashRounds.
     * @example
     * // Create many CrashRounds
     * const crashRound = await prisma.crashRound.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CrashRoundCreateManyArgs>(args?: SelectSubset<T, CrashRoundCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many CrashRounds and returns the data saved in the database.
     * @param {CrashRoundCreateManyAndReturnArgs} args - Arguments to create many CrashRounds.
     * @example
     * // Create many CrashRounds
     * const crashRound = await prisma.crashRound.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many CrashRounds and only return the `id`
     * const crashRoundWithIdOnly = await prisma.crashRound.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends CrashRoundCreateManyAndReturnArgs>(args?: SelectSubset<T, CrashRoundCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a CrashRound.
     * @param {CrashRoundDeleteArgs} args - Arguments to delete one CrashRound.
     * @example
     * // Delete one CrashRound
     * const CrashRound = await prisma.crashRound.delete({
     *   where: {
     *     // ... filter to delete one CrashRound
     *   }
     * })
     * 
     */
    delete<T extends CrashRoundDeleteArgs>(args: SelectSubset<T, CrashRoundDeleteArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one CrashRound.
     * @param {CrashRoundUpdateArgs} args - Arguments to update one CrashRound.
     * @example
     * // Update one CrashRound
     * const crashRound = await prisma.crashRound.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CrashRoundUpdateArgs>(args: SelectSubset<T, CrashRoundUpdateArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more CrashRounds.
     * @param {CrashRoundDeleteManyArgs} args - Arguments to filter CrashRounds to delete.
     * @example
     * // Delete a few CrashRounds
     * const { count } = await prisma.crashRound.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CrashRoundDeleteManyArgs>(args?: SelectSubset<T, CrashRoundDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more CrashRounds.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashRoundUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many CrashRounds
     * const crashRound = await prisma.crashRound.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CrashRoundUpdateManyArgs>(args: SelectSubset<T, CrashRoundUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one CrashRound.
     * @param {CrashRoundUpsertArgs} args - Arguments to update or create a CrashRound.
     * @example
     * // Update or create a CrashRound
     * const crashRound = await prisma.crashRound.upsert({
     *   create: {
     *     // ... data to create a CrashRound
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the CrashRound we want to update
     *   }
     * })
     */
    upsert<T extends CrashRoundUpsertArgs>(args: SelectSubset<T, CrashRoundUpsertArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of CrashRounds.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashRoundCountArgs} args - Arguments to filter CrashRounds to count.
     * @example
     * // Count the number of CrashRounds
     * const count = await prisma.crashRound.count({
     *   where: {
     *     // ... the filter for the CrashRounds we want to count
     *   }
     * })
    **/
    count<T extends CrashRoundCountArgs>(
      args?: Subset<T, CrashRoundCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CrashRoundCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a CrashRound.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashRoundAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CrashRoundAggregateArgs>(args: Subset<T, CrashRoundAggregateArgs>): Prisma.PrismaPromise<GetCrashRoundAggregateType<T>>

    /**
     * Group by CrashRound.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashRoundGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CrashRoundGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CrashRoundGroupByArgs['orderBy'] }
        : { orderBy?: CrashRoundGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CrashRoundGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCrashRoundGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the CrashRound model
   */
  readonly fields: CrashRoundFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for CrashRound.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CrashRoundClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    bets<T extends CrashRound$betsArgs<ExtArgs> = {}>(args?: Subset<T, CrashRound$betsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the CrashRound model
   */ 
  interface CrashRoundFieldRefs {
    readonly id: FieldRef<"CrashRound", 'Int'>
    readonly multiplier: FieldRef<"CrashRound", 'Decimal'>
    readonly seed: FieldRef<"CrashRound", 'String'>
    readonly status: FieldRef<"CrashRound", 'String'>
    readonly createdAt: FieldRef<"CrashRound", 'DateTime'>
    readonly crashedAt: FieldRef<"CrashRound", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * CrashRound findUnique
   */
  export type CrashRoundFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * Filter, which CrashRound to fetch.
     */
    where: CrashRoundWhereUniqueInput
  }

  /**
   * CrashRound findUniqueOrThrow
   */
  export type CrashRoundFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * Filter, which CrashRound to fetch.
     */
    where: CrashRoundWhereUniqueInput
  }

  /**
   * CrashRound findFirst
   */
  export type CrashRoundFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * Filter, which CrashRound to fetch.
     */
    where?: CrashRoundWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrashRounds to fetch.
     */
    orderBy?: CrashRoundOrderByWithRelationInput | CrashRoundOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CrashRounds.
     */
    cursor?: CrashRoundWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrashRounds from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrashRounds.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CrashRounds.
     */
    distinct?: CrashRoundScalarFieldEnum | CrashRoundScalarFieldEnum[]
  }

  /**
   * CrashRound findFirstOrThrow
   */
  export type CrashRoundFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * Filter, which CrashRound to fetch.
     */
    where?: CrashRoundWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrashRounds to fetch.
     */
    orderBy?: CrashRoundOrderByWithRelationInput | CrashRoundOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CrashRounds.
     */
    cursor?: CrashRoundWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrashRounds from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrashRounds.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CrashRounds.
     */
    distinct?: CrashRoundScalarFieldEnum | CrashRoundScalarFieldEnum[]
  }

  /**
   * CrashRound findMany
   */
  export type CrashRoundFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * Filter, which CrashRounds to fetch.
     */
    where?: CrashRoundWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrashRounds to fetch.
     */
    orderBy?: CrashRoundOrderByWithRelationInput | CrashRoundOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing CrashRounds.
     */
    cursor?: CrashRoundWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrashRounds from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrashRounds.
     */
    skip?: number
    distinct?: CrashRoundScalarFieldEnum | CrashRoundScalarFieldEnum[]
  }

  /**
   * CrashRound create
   */
  export type CrashRoundCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * The data needed to create a CrashRound.
     */
    data: XOR<CrashRoundCreateInput, CrashRoundUncheckedCreateInput>
  }

  /**
   * CrashRound createMany
   */
  export type CrashRoundCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many CrashRounds.
     */
    data: CrashRoundCreateManyInput | CrashRoundCreateManyInput[]
  }

  /**
   * CrashRound createManyAndReturn
   */
  export type CrashRoundCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many CrashRounds.
     */
    data: CrashRoundCreateManyInput | CrashRoundCreateManyInput[]
  }

  /**
   * CrashRound update
   */
  export type CrashRoundUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * The data needed to update a CrashRound.
     */
    data: XOR<CrashRoundUpdateInput, CrashRoundUncheckedUpdateInput>
    /**
     * Choose, which CrashRound to update.
     */
    where: CrashRoundWhereUniqueInput
  }

  /**
   * CrashRound updateMany
   */
  export type CrashRoundUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update CrashRounds.
     */
    data: XOR<CrashRoundUpdateManyMutationInput, CrashRoundUncheckedUpdateManyInput>
    /**
     * Filter which CrashRounds to update
     */
    where?: CrashRoundWhereInput
  }

  /**
   * CrashRound upsert
   */
  export type CrashRoundUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * The filter to search for the CrashRound to update in case it exists.
     */
    where: CrashRoundWhereUniqueInput
    /**
     * In case the CrashRound found by the `where` argument doesn't exist, create a new CrashRound with this data.
     */
    create: XOR<CrashRoundCreateInput, CrashRoundUncheckedCreateInput>
    /**
     * In case the CrashRound was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CrashRoundUpdateInput, CrashRoundUncheckedUpdateInput>
  }

  /**
   * CrashRound delete
   */
  export type CrashRoundDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
    /**
     * Filter which CrashRound to delete.
     */
    where: CrashRoundWhereUniqueInput
  }

  /**
   * CrashRound deleteMany
   */
  export type CrashRoundDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CrashRounds to delete
     */
    where?: CrashRoundWhereInput
  }

  /**
   * CrashRound.bets
   */
  export type CrashRound$betsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    where?: CrashBetWhereInput
    orderBy?: CrashBetOrderByWithRelationInput | CrashBetOrderByWithRelationInput[]
    cursor?: CrashBetWhereUniqueInput
    take?: number
    skip?: number
    distinct?: CrashBetScalarFieldEnum | CrashBetScalarFieldEnum[]
  }

  /**
   * CrashRound without action
   */
  export type CrashRoundDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashRound
     */
    select?: CrashRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashRoundInclude<ExtArgs> | null
  }


  /**
   * Model CrashBet
   */

  export type AggregateCrashBet = {
    _count: CrashBetCountAggregateOutputType | null
    _avg: CrashBetAvgAggregateOutputType | null
    _sum: CrashBetSumAggregateOutputType | null
    _min: CrashBetMinAggregateOutputType | null
    _max: CrashBetMaxAggregateOutputType | null
  }

  export type CrashBetAvgAggregateOutputType = {
    id: number | null
    roundId: number | null
    userId: number | null
    betTon: Decimal | null
    cashoutAt: Decimal | null
    winTon: Decimal | null
  }

  export type CrashBetSumAggregateOutputType = {
    id: number | null
    roundId: number | null
    userId: number | null
    betTon: Decimal | null
    cashoutAt: Decimal | null
    winTon: Decimal | null
  }

  export type CrashBetMinAggregateOutputType = {
    id: number | null
    roundId: number | null
    userId: number | null
    betTon: Decimal | null
    cashoutAt: Decimal | null
    winTon: Decimal | null
    createdAt: Date | null
  }

  export type CrashBetMaxAggregateOutputType = {
    id: number | null
    roundId: number | null
    userId: number | null
    betTon: Decimal | null
    cashoutAt: Decimal | null
    winTon: Decimal | null
    createdAt: Date | null
  }

  export type CrashBetCountAggregateOutputType = {
    id: number
    roundId: number
    userId: number
    betTon: number
    cashoutAt: number
    winTon: number
    createdAt: number
    _all: number
  }


  export type CrashBetAvgAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    cashoutAt?: true
    winTon?: true
  }

  export type CrashBetSumAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    cashoutAt?: true
    winTon?: true
  }

  export type CrashBetMinAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    cashoutAt?: true
    winTon?: true
    createdAt?: true
  }

  export type CrashBetMaxAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    cashoutAt?: true
    winTon?: true
    createdAt?: true
  }

  export type CrashBetCountAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    cashoutAt?: true
    winTon?: true
    createdAt?: true
    _all?: true
  }

  export type CrashBetAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CrashBet to aggregate.
     */
    where?: CrashBetWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrashBets to fetch.
     */
    orderBy?: CrashBetOrderByWithRelationInput | CrashBetOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CrashBetWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrashBets from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrashBets.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned CrashBets
    **/
    _count?: true | CrashBetCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: CrashBetAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: CrashBetSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CrashBetMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CrashBetMaxAggregateInputType
  }

  export type GetCrashBetAggregateType<T extends CrashBetAggregateArgs> = {
        [P in keyof T & keyof AggregateCrashBet]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateCrashBet[P]>
      : GetScalarType<T[P], AggregateCrashBet[P]>
  }




  export type CrashBetGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CrashBetWhereInput
    orderBy?: CrashBetOrderByWithAggregationInput | CrashBetOrderByWithAggregationInput[]
    by: CrashBetScalarFieldEnum[] | CrashBetScalarFieldEnum
    having?: CrashBetScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CrashBetCountAggregateInputType | true
    _avg?: CrashBetAvgAggregateInputType
    _sum?: CrashBetSumAggregateInputType
    _min?: CrashBetMinAggregateInputType
    _max?: CrashBetMaxAggregateInputType
  }

  export type CrashBetGroupByOutputType = {
    id: number
    roundId: number
    userId: number
    betTon: Decimal
    cashoutAt: Decimal | null
    winTon: Decimal | null
    createdAt: Date
    _count: CrashBetCountAggregateOutputType | null
    _avg: CrashBetAvgAggregateOutputType | null
    _sum: CrashBetSumAggregateOutputType | null
    _min: CrashBetMinAggregateOutputType | null
    _max: CrashBetMaxAggregateOutputType | null
  }

  type GetCrashBetGroupByPayload<T extends CrashBetGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CrashBetGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CrashBetGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CrashBetGroupByOutputType[P]>
            : GetScalarType<T[P], CrashBetGroupByOutputType[P]>
        }
      >
    >


  export type CrashBetSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    roundId?: boolean
    userId?: boolean
    betTon?: boolean
    cashoutAt?: boolean
    winTon?: boolean
    createdAt?: boolean
    round?: boolean | CrashRoundDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["crashBet"]>

  export type CrashBetSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    roundId?: boolean
    userId?: boolean
    betTon?: boolean
    cashoutAt?: boolean
    winTon?: boolean
    createdAt?: boolean
    round?: boolean | CrashRoundDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["crashBet"]>

  export type CrashBetSelectScalar = {
    id?: boolean
    roundId?: boolean
    userId?: boolean
    betTon?: boolean
    cashoutAt?: boolean
    winTon?: boolean
    createdAt?: boolean
  }

  export type CrashBetInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    round?: boolean | CrashRoundDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type CrashBetIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    round?: boolean | CrashRoundDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $CrashBetPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "CrashBet"
    objects: {
      round: Prisma.$CrashRoundPayload<ExtArgs>
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      roundId: number
      userId: number
      betTon: Prisma.Decimal
      cashoutAt: Prisma.Decimal | null
      winTon: Prisma.Decimal | null
      createdAt: Date
    }, ExtArgs["result"]["crashBet"]>
    composites: {}
  }

  type CrashBetGetPayload<S extends boolean | null | undefined | CrashBetDefaultArgs> = $Result.GetResult<Prisma.$CrashBetPayload, S>

  type CrashBetCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<CrashBetFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: CrashBetCountAggregateInputType | true
    }

  export interface CrashBetDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['CrashBet'], meta: { name: 'CrashBet' } }
    /**
     * Find zero or one CrashBet that matches the filter.
     * @param {CrashBetFindUniqueArgs} args - Arguments to find a CrashBet
     * @example
     * // Get one CrashBet
     * const crashBet = await prisma.crashBet.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CrashBetFindUniqueArgs>(args: SelectSubset<T, CrashBetFindUniqueArgs<ExtArgs>>): Prisma__CrashBetClient<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one CrashBet that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {CrashBetFindUniqueOrThrowArgs} args - Arguments to find a CrashBet
     * @example
     * // Get one CrashBet
     * const crashBet = await prisma.crashBet.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CrashBetFindUniqueOrThrowArgs>(args: SelectSubset<T, CrashBetFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CrashBetClient<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first CrashBet that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashBetFindFirstArgs} args - Arguments to find a CrashBet
     * @example
     * // Get one CrashBet
     * const crashBet = await prisma.crashBet.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CrashBetFindFirstArgs>(args?: SelectSubset<T, CrashBetFindFirstArgs<ExtArgs>>): Prisma__CrashBetClient<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first CrashBet that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashBetFindFirstOrThrowArgs} args - Arguments to find a CrashBet
     * @example
     * // Get one CrashBet
     * const crashBet = await prisma.crashBet.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CrashBetFindFirstOrThrowArgs>(args?: SelectSubset<T, CrashBetFindFirstOrThrowArgs<ExtArgs>>): Prisma__CrashBetClient<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more CrashBets that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashBetFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all CrashBets
     * const crashBets = await prisma.crashBet.findMany()
     * 
     * // Get first 10 CrashBets
     * const crashBets = await prisma.crashBet.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const crashBetWithIdOnly = await prisma.crashBet.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CrashBetFindManyArgs>(args?: SelectSubset<T, CrashBetFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a CrashBet.
     * @param {CrashBetCreateArgs} args - Arguments to create a CrashBet.
     * @example
     * // Create one CrashBet
     * const CrashBet = await prisma.crashBet.create({
     *   data: {
     *     // ... data to create a CrashBet
     *   }
     * })
     * 
     */
    create<T extends CrashBetCreateArgs>(args: SelectSubset<T, CrashBetCreateArgs<ExtArgs>>): Prisma__CrashBetClient<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many CrashBets.
     * @param {CrashBetCreateManyArgs} args - Arguments to create many CrashBets.
     * @example
     * // Create many CrashBets
     * const crashBet = await prisma.crashBet.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CrashBetCreateManyArgs>(args?: SelectSubset<T, CrashBetCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many CrashBets and returns the data saved in the database.
     * @param {CrashBetCreateManyAndReturnArgs} args - Arguments to create many CrashBets.
     * @example
     * // Create many CrashBets
     * const crashBet = await prisma.crashBet.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many CrashBets and only return the `id`
     * const crashBetWithIdOnly = await prisma.crashBet.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends CrashBetCreateManyAndReturnArgs>(args?: SelectSubset<T, CrashBetCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a CrashBet.
     * @param {CrashBetDeleteArgs} args - Arguments to delete one CrashBet.
     * @example
     * // Delete one CrashBet
     * const CrashBet = await prisma.crashBet.delete({
     *   where: {
     *     // ... filter to delete one CrashBet
     *   }
     * })
     * 
     */
    delete<T extends CrashBetDeleteArgs>(args: SelectSubset<T, CrashBetDeleteArgs<ExtArgs>>): Prisma__CrashBetClient<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one CrashBet.
     * @param {CrashBetUpdateArgs} args - Arguments to update one CrashBet.
     * @example
     * // Update one CrashBet
     * const crashBet = await prisma.crashBet.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CrashBetUpdateArgs>(args: SelectSubset<T, CrashBetUpdateArgs<ExtArgs>>): Prisma__CrashBetClient<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more CrashBets.
     * @param {CrashBetDeleteManyArgs} args - Arguments to filter CrashBets to delete.
     * @example
     * // Delete a few CrashBets
     * const { count } = await prisma.crashBet.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CrashBetDeleteManyArgs>(args?: SelectSubset<T, CrashBetDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more CrashBets.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashBetUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many CrashBets
     * const crashBet = await prisma.crashBet.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CrashBetUpdateManyArgs>(args: SelectSubset<T, CrashBetUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one CrashBet.
     * @param {CrashBetUpsertArgs} args - Arguments to update or create a CrashBet.
     * @example
     * // Update or create a CrashBet
     * const crashBet = await prisma.crashBet.upsert({
     *   create: {
     *     // ... data to create a CrashBet
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the CrashBet we want to update
     *   }
     * })
     */
    upsert<T extends CrashBetUpsertArgs>(args: SelectSubset<T, CrashBetUpsertArgs<ExtArgs>>): Prisma__CrashBetClient<$Result.GetResult<Prisma.$CrashBetPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of CrashBets.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashBetCountArgs} args - Arguments to filter CrashBets to count.
     * @example
     * // Count the number of CrashBets
     * const count = await prisma.crashBet.count({
     *   where: {
     *     // ... the filter for the CrashBets we want to count
     *   }
     * })
    **/
    count<T extends CrashBetCountArgs>(
      args?: Subset<T, CrashBetCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CrashBetCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a CrashBet.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashBetAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CrashBetAggregateArgs>(args: Subset<T, CrashBetAggregateArgs>): Prisma.PrismaPromise<GetCrashBetAggregateType<T>>

    /**
     * Group by CrashBet.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CrashBetGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CrashBetGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CrashBetGroupByArgs['orderBy'] }
        : { orderBy?: CrashBetGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CrashBetGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCrashBetGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the CrashBet model
   */
  readonly fields: CrashBetFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for CrashBet.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CrashBetClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    round<T extends CrashRoundDefaultArgs<ExtArgs> = {}>(args?: Subset<T, CrashRoundDefaultArgs<ExtArgs>>): Prisma__CrashRoundClient<$Result.GetResult<Prisma.$CrashRoundPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the CrashBet model
   */ 
  interface CrashBetFieldRefs {
    readonly id: FieldRef<"CrashBet", 'Int'>
    readonly roundId: FieldRef<"CrashBet", 'Int'>
    readonly userId: FieldRef<"CrashBet", 'Int'>
    readonly betTon: FieldRef<"CrashBet", 'Decimal'>
    readonly cashoutAt: FieldRef<"CrashBet", 'Decimal'>
    readonly winTon: FieldRef<"CrashBet", 'Decimal'>
    readonly createdAt: FieldRef<"CrashBet", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * CrashBet findUnique
   */
  export type CrashBetFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * Filter, which CrashBet to fetch.
     */
    where: CrashBetWhereUniqueInput
  }

  /**
   * CrashBet findUniqueOrThrow
   */
  export type CrashBetFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * Filter, which CrashBet to fetch.
     */
    where: CrashBetWhereUniqueInput
  }

  /**
   * CrashBet findFirst
   */
  export type CrashBetFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * Filter, which CrashBet to fetch.
     */
    where?: CrashBetWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrashBets to fetch.
     */
    orderBy?: CrashBetOrderByWithRelationInput | CrashBetOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CrashBets.
     */
    cursor?: CrashBetWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrashBets from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrashBets.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CrashBets.
     */
    distinct?: CrashBetScalarFieldEnum | CrashBetScalarFieldEnum[]
  }

  /**
   * CrashBet findFirstOrThrow
   */
  export type CrashBetFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * Filter, which CrashBet to fetch.
     */
    where?: CrashBetWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrashBets to fetch.
     */
    orderBy?: CrashBetOrderByWithRelationInput | CrashBetOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CrashBets.
     */
    cursor?: CrashBetWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrashBets from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrashBets.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CrashBets.
     */
    distinct?: CrashBetScalarFieldEnum | CrashBetScalarFieldEnum[]
  }

  /**
   * CrashBet findMany
   */
  export type CrashBetFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * Filter, which CrashBets to fetch.
     */
    where?: CrashBetWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CrashBets to fetch.
     */
    orderBy?: CrashBetOrderByWithRelationInput | CrashBetOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing CrashBets.
     */
    cursor?: CrashBetWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CrashBets from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CrashBets.
     */
    skip?: number
    distinct?: CrashBetScalarFieldEnum | CrashBetScalarFieldEnum[]
  }

  /**
   * CrashBet create
   */
  export type CrashBetCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * The data needed to create a CrashBet.
     */
    data: XOR<CrashBetCreateInput, CrashBetUncheckedCreateInput>
  }

  /**
   * CrashBet createMany
   */
  export type CrashBetCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many CrashBets.
     */
    data: CrashBetCreateManyInput | CrashBetCreateManyInput[]
  }

  /**
   * CrashBet createManyAndReturn
   */
  export type CrashBetCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many CrashBets.
     */
    data: CrashBetCreateManyInput | CrashBetCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * CrashBet update
   */
  export type CrashBetUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * The data needed to update a CrashBet.
     */
    data: XOR<CrashBetUpdateInput, CrashBetUncheckedUpdateInput>
    /**
     * Choose, which CrashBet to update.
     */
    where: CrashBetWhereUniqueInput
  }

  /**
   * CrashBet updateMany
   */
  export type CrashBetUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update CrashBets.
     */
    data: XOR<CrashBetUpdateManyMutationInput, CrashBetUncheckedUpdateManyInput>
    /**
     * Filter which CrashBets to update
     */
    where?: CrashBetWhereInput
  }

  /**
   * CrashBet upsert
   */
  export type CrashBetUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * The filter to search for the CrashBet to update in case it exists.
     */
    where: CrashBetWhereUniqueInput
    /**
     * In case the CrashBet found by the `where` argument doesn't exist, create a new CrashBet with this data.
     */
    create: XOR<CrashBetCreateInput, CrashBetUncheckedCreateInput>
    /**
     * In case the CrashBet was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CrashBetUpdateInput, CrashBetUncheckedUpdateInput>
  }

  /**
   * CrashBet delete
   */
  export type CrashBetDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
    /**
     * Filter which CrashBet to delete.
     */
    where: CrashBetWhereUniqueInput
  }

  /**
   * CrashBet deleteMany
   */
  export type CrashBetDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CrashBets to delete
     */
    where?: CrashBetWhereInput
  }

  /**
   * CrashBet without action
   */
  export type CrashBetDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CrashBet
     */
    select?: CrashBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CrashBetInclude<ExtArgs> | null
  }


  /**
   * Model DoubleRound
   */

  export type AggregateDoubleRound = {
    _count: DoubleRoundCountAggregateOutputType | null
    _avg: DoubleRoundAvgAggregateOutputType | null
    _sum: DoubleRoundSumAggregateOutputType | null
    _min: DoubleRoundMinAggregateOutputType | null
    _max: DoubleRoundMaxAggregateOutputType | null
  }

  export type DoubleRoundAvgAggregateOutputType = {
    id: number | null
  }

  export type DoubleRoundSumAggregateOutputType = {
    id: number | null
  }

  export type DoubleRoundMinAggregateOutputType = {
    id: number | null
    result: string | null
    seed: string | null
    createdAt: Date | null
  }

  export type DoubleRoundMaxAggregateOutputType = {
    id: number | null
    result: string | null
    seed: string | null
    createdAt: Date | null
  }

  export type DoubleRoundCountAggregateOutputType = {
    id: number
    result: number
    seed: number
    createdAt: number
    _all: number
  }


  export type DoubleRoundAvgAggregateInputType = {
    id?: true
  }

  export type DoubleRoundSumAggregateInputType = {
    id?: true
  }

  export type DoubleRoundMinAggregateInputType = {
    id?: true
    result?: true
    seed?: true
    createdAt?: true
  }

  export type DoubleRoundMaxAggregateInputType = {
    id?: true
    result?: true
    seed?: true
    createdAt?: true
  }

  export type DoubleRoundCountAggregateInputType = {
    id?: true
    result?: true
    seed?: true
    createdAt?: true
    _all?: true
  }

  export type DoubleRoundAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which DoubleRound to aggregate.
     */
    where?: DoubleRoundWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DoubleRounds to fetch.
     */
    orderBy?: DoubleRoundOrderByWithRelationInput | DoubleRoundOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: DoubleRoundWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DoubleRounds from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DoubleRounds.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned DoubleRounds
    **/
    _count?: true | DoubleRoundCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: DoubleRoundAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: DoubleRoundSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: DoubleRoundMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: DoubleRoundMaxAggregateInputType
  }

  export type GetDoubleRoundAggregateType<T extends DoubleRoundAggregateArgs> = {
        [P in keyof T & keyof AggregateDoubleRound]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateDoubleRound[P]>
      : GetScalarType<T[P], AggregateDoubleRound[P]>
  }




  export type DoubleRoundGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: DoubleRoundWhereInput
    orderBy?: DoubleRoundOrderByWithAggregationInput | DoubleRoundOrderByWithAggregationInput[]
    by: DoubleRoundScalarFieldEnum[] | DoubleRoundScalarFieldEnum
    having?: DoubleRoundScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: DoubleRoundCountAggregateInputType | true
    _avg?: DoubleRoundAvgAggregateInputType
    _sum?: DoubleRoundSumAggregateInputType
    _min?: DoubleRoundMinAggregateInputType
    _max?: DoubleRoundMaxAggregateInputType
  }

  export type DoubleRoundGroupByOutputType = {
    id: number
    result: string
    seed: string
    createdAt: Date
    _count: DoubleRoundCountAggregateOutputType | null
    _avg: DoubleRoundAvgAggregateOutputType | null
    _sum: DoubleRoundSumAggregateOutputType | null
    _min: DoubleRoundMinAggregateOutputType | null
    _max: DoubleRoundMaxAggregateOutputType | null
  }

  type GetDoubleRoundGroupByPayload<T extends DoubleRoundGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<DoubleRoundGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof DoubleRoundGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], DoubleRoundGroupByOutputType[P]>
            : GetScalarType<T[P], DoubleRoundGroupByOutputType[P]>
        }
      >
    >


  export type DoubleRoundSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    result?: boolean
    seed?: boolean
    createdAt?: boolean
    bets?: boolean | DoubleRound$betsArgs<ExtArgs>
    _count?: boolean | DoubleRoundCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["doubleRound"]>

  export type DoubleRoundSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    result?: boolean
    seed?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["doubleRound"]>

  export type DoubleRoundSelectScalar = {
    id?: boolean
    result?: boolean
    seed?: boolean
    createdAt?: boolean
  }

  export type DoubleRoundInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    bets?: boolean | DoubleRound$betsArgs<ExtArgs>
    _count?: boolean | DoubleRoundCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type DoubleRoundIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $DoubleRoundPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "DoubleRound"
    objects: {
      bets: Prisma.$DoubleBetPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      result: string
      seed: string
      createdAt: Date
    }, ExtArgs["result"]["doubleRound"]>
    composites: {}
  }

  type DoubleRoundGetPayload<S extends boolean | null | undefined | DoubleRoundDefaultArgs> = $Result.GetResult<Prisma.$DoubleRoundPayload, S>

  type DoubleRoundCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<DoubleRoundFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: DoubleRoundCountAggregateInputType | true
    }

  export interface DoubleRoundDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['DoubleRound'], meta: { name: 'DoubleRound' } }
    /**
     * Find zero or one DoubleRound that matches the filter.
     * @param {DoubleRoundFindUniqueArgs} args - Arguments to find a DoubleRound
     * @example
     * // Get one DoubleRound
     * const doubleRound = await prisma.doubleRound.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends DoubleRoundFindUniqueArgs>(args: SelectSubset<T, DoubleRoundFindUniqueArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one DoubleRound that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {DoubleRoundFindUniqueOrThrowArgs} args - Arguments to find a DoubleRound
     * @example
     * // Get one DoubleRound
     * const doubleRound = await prisma.doubleRound.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends DoubleRoundFindUniqueOrThrowArgs>(args: SelectSubset<T, DoubleRoundFindUniqueOrThrowArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first DoubleRound that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleRoundFindFirstArgs} args - Arguments to find a DoubleRound
     * @example
     * // Get one DoubleRound
     * const doubleRound = await prisma.doubleRound.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends DoubleRoundFindFirstArgs>(args?: SelectSubset<T, DoubleRoundFindFirstArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first DoubleRound that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleRoundFindFirstOrThrowArgs} args - Arguments to find a DoubleRound
     * @example
     * // Get one DoubleRound
     * const doubleRound = await prisma.doubleRound.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends DoubleRoundFindFirstOrThrowArgs>(args?: SelectSubset<T, DoubleRoundFindFirstOrThrowArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more DoubleRounds that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleRoundFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all DoubleRounds
     * const doubleRounds = await prisma.doubleRound.findMany()
     * 
     * // Get first 10 DoubleRounds
     * const doubleRounds = await prisma.doubleRound.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const doubleRoundWithIdOnly = await prisma.doubleRound.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends DoubleRoundFindManyArgs>(args?: SelectSubset<T, DoubleRoundFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a DoubleRound.
     * @param {DoubleRoundCreateArgs} args - Arguments to create a DoubleRound.
     * @example
     * // Create one DoubleRound
     * const DoubleRound = await prisma.doubleRound.create({
     *   data: {
     *     // ... data to create a DoubleRound
     *   }
     * })
     * 
     */
    create<T extends DoubleRoundCreateArgs>(args: SelectSubset<T, DoubleRoundCreateArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many DoubleRounds.
     * @param {DoubleRoundCreateManyArgs} args - Arguments to create many DoubleRounds.
     * @example
     * // Create many DoubleRounds
     * const doubleRound = await prisma.doubleRound.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends DoubleRoundCreateManyArgs>(args?: SelectSubset<T, DoubleRoundCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many DoubleRounds and returns the data saved in the database.
     * @param {DoubleRoundCreateManyAndReturnArgs} args - Arguments to create many DoubleRounds.
     * @example
     * // Create many DoubleRounds
     * const doubleRound = await prisma.doubleRound.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many DoubleRounds and only return the `id`
     * const doubleRoundWithIdOnly = await prisma.doubleRound.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends DoubleRoundCreateManyAndReturnArgs>(args?: SelectSubset<T, DoubleRoundCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a DoubleRound.
     * @param {DoubleRoundDeleteArgs} args - Arguments to delete one DoubleRound.
     * @example
     * // Delete one DoubleRound
     * const DoubleRound = await prisma.doubleRound.delete({
     *   where: {
     *     // ... filter to delete one DoubleRound
     *   }
     * })
     * 
     */
    delete<T extends DoubleRoundDeleteArgs>(args: SelectSubset<T, DoubleRoundDeleteArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one DoubleRound.
     * @param {DoubleRoundUpdateArgs} args - Arguments to update one DoubleRound.
     * @example
     * // Update one DoubleRound
     * const doubleRound = await prisma.doubleRound.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends DoubleRoundUpdateArgs>(args: SelectSubset<T, DoubleRoundUpdateArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more DoubleRounds.
     * @param {DoubleRoundDeleteManyArgs} args - Arguments to filter DoubleRounds to delete.
     * @example
     * // Delete a few DoubleRounds
     * const { count } = await prisma.doubleRound.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends DoubleRoundDeleteManyArgs>(args?: SelectSubset<T, DoubleRoundDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more DoubleRounds.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleRoundUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many DoubleRounds
     * const doubleRound = await prisma.doubleRound.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends DoubleRoundUpdateManyArgs>(args: SelectSubset<T, DoubleRoundUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one DoubleRound.
     * @param {DoubleRoundUpsertArgs} args - Arguments to update or create a DoubleRound.
     * @example
     * // Update or create a DoubleRound
     * const doubleRound = await prisma.doubleRound.upsert({
     *   create: {
     *     // ... data to create a DoubleRound
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the DoubleRound we want to update
     *   }
     * })
     */
    upsert<T extends DoubleRoundUpsertArgs>(args: SelectSubset<T, DoubleRoundUpsertArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of DoubleRounds.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleRoundCountArgs} args - Arguments to filter DoubleRounds to count.
     * @example
     * // Count the number of DoubleRounds
     * const count = await prisma.doubleRound.count({
     *   where: {
     *     // ... the filter for the DoubleRounds we want to count
     *   }
     * })
    **/
    count<T extends DoubleRoundCountArgs>(
      args?: Subset<T, DoubleRoundCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], DoubleRoundCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a DoubleRound.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleRoundAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends DoubleRoundAggregateArgs>(args: Subset<T, DoubleRoundAggregateArgs>): Prisma.PrismaPromise<GetDoubleRoundAggregateType<T>>

    /**
     * Group by DoubleRound.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleRoundGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends DoubleRoundGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: DoubleRoundGroupByArgs['orderBy'] }
        : { orderBy?: DoubleRoundGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, DoubleRoundGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDoubleRoundGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the DoubleRound model
   */
  readonly fields: DoubleRoundFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for DoubleRound.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__DoubleRoundClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    bets<T extends DoubleRound$betsArgs<ExtArgs> = {}>(args?: Subset<T, DoubleRound$betsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the DoubleRound model
   */ 
  interface DoubleRoundFieldRefs {
    readonly id: FieldRef<"DoubleRound", 'Int'>
    readonly result: FieldRef<"DoubleRound", 'String'>
    readonly seed: FieldRef<"DoubleRound", 'String'>
    readonly createdAt: FieldRef<"DoubleRound", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * DoubleRound findUnique
   */
  export type DoubleRoundFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * Filter, which DoubleRound to fetch.
     */
    where: DoubleRoundWhereUniqueInput
  }

  /**
   * DoubleRound findUniqueOrThrow
   */
  export type DoubleRoundFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * Filter, which DoubleRound to fetch.
     */
    where: DoubleRoundWhereUniqueInput
  }

  /**
   * DoubleRound findFirst
   */
  export type DoubleRoundFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * Filter, which DoubleRound to fetch.
     */
    where?: DoubleRoundWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DoubleRounds to fetch.
     */
    orderBy?: DoubleRoundOrderByWithRelationInput | DoubleRoundOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for DoubleRounds.
     */
    cursor?: DoubleRoundWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DoubleRounds from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DoubleRounds.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of DoubleRounds.
     */
    distinct?: DoubleRoundScalarFieldEnum | DoubleRoundScalarFieldEnum[]
  }

  /**
   * DoubleRound findFirstOrThrow
   */
  export type DoubleRoundFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * Filter, which DoubleRound to fetch.
     */
    where?: DoubleRoundWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DoubleRounds to fetch.
     */
    orderBy?: DoubleRoundOrderByWithRelationInput | DoubleRoundOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for DoubleRounds.
     */
    cursor?: DoubleRoundWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DoubleRounds from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DoubleRounds.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of DoubleRounds.
     */
    distinct?: DoubleRoundScalarFieldEnum | DoubleRoundScalarFieldEnum[]
  }

  /**
   * DoubleRound findMany
   */
  export type DoubleRoundFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * Filter, which DoubleRounds to fetch.
     */
    where?: DoubleRoundWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DoubleRounds to fetch.
     */
    orderBy?: DoubleRoundOrderByWithRelationInput | DoubleRoundOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing DoubleRounds.
     */
    cursor?: DoubleRoundWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DoubleRounds from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DoubleRounds.
     */
    skip?: number
    distinct?: DoubleRoundScalarFieldEnum | DoubleRoundScalarFieldEnum[]
  }

  /**
   * DoubleRound create
   */
  export type DoubleRoundCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * The data needed to create a DoubleRound.
     */
    data: XOR<DoubleRoundCreateInput, DoubleRoundUncheckedCreateInput>
  }

  /**
   * DoubleRound createMany
   */
  export type DoubleRoundCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many DoubleRounds.
     */
    data: DoubleRoundCreateManyInput | DoubleRoundCreateManyInput[]
  }

  /**
   * DoubleRound createManyAndReturn
   */
  export type DoubleRoundCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many DoubleRounds.
     */
    data: DoubleRoundCreateManyInput | DoubleRoundCreateManyInput[]
  }

  /**
   * DoubleRound update
   */
  export type DoubleRoundUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * The data needed to update a DoubleRound.
     */
    data: XOR<DoubleRoundUpdateInput, DoubleRoundUncheckedUpdateInput>
    /**
     * Choose, which DoubleRound to update.
     */
    where: DoubleRoundWhereUniqueInput
  }

  /**
   * DoubleRound updateMany
   */
  export type DoubleRoundUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update DoubleRounds.
     */
    data: XOR<DoubleRoundUpdateManyMutationInput, DoubleRoundUncheckedUpdateManyInput>
    /**
     * Filter which DoubleRounds to update
     */
    where?: DoubleRoundWhereInput
  }

  /**
   * DoubleRound upsert
   */
  export type DoubleRoundUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * The filter to search for the DoubleRound to update in case it exists.
     */
    where: DoubleRoundWhereUniqueInput
    /**
     * In case the DoubleRound found by the `where` argument doesn't exist, create a new DoubleRound with this data.
     */
    create: XOR<DoubleRoundCreateInput, DoubleRoundUncheckedCreateInput>
    /**
     * In case the DoubleRound was found with the provided `where` argument, update it with this data.
     */
    update: XOR<DoubleRoundUpdateInput, DoubleRoundUncheckedUpdateInput>
  }

  /**
   * DoubleRound delete
   */
  export type DoubleRoundDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
    /**
     * Filter which DoubleRound to delete.
     */
    where: DoubleRoundWhereUniqueInput
  }

  /**
   * DoubleRound deleteMany
   */
  export type DoubleRoundDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which DoubleRounds to delete
     */
    where?: DoubleRoundWhereInput
  }

  /**
   * DoubleRound.bets
   */
  export type DoubleRound$betsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    where?: DoubleBetWhereInput
    orderBy?: DoubleBetOrderByWithRelationInput | DoubleBetOrderByWithRelationInput[]
    cursor?: DoubleBetWhereUniqueInput
    take?: number
    skip?: number
    distinct?: DoubleBetScalarFieldEnum | DoubleBetScalarFieldEnum[]
  }

  /**
   * DoubleRound without action
   */
  export type DoubleRoundDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleRound
     */
    select?: DoubleRoundSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleRoundInclude<ExtArgs> | null
  }


  /**
   * Model DoubleBet
   */

  export type AggregateDoubleBet = {
    _count: DoubleBetCountAggregateOutputType | null
    _avg: DoubleBetAvgAggregateOutputType | null
    _sum: DoubleBetSumAggregateOutputType | null
    _min: DoubleBetMinAggregateOutputType | null
    _max: DoubleBetMaxAggregateOutputType | null
  }

  export type DoubleBetAvgAggregateOutputType = {
    id: number | null
    roundId: number | null
    userId: number | null
    betTon: Decimal | null
    winTon: Decimal | null
  }

  export type DoubleBetSumAggregateOutputType = {
    id: number | null
    roundId: number | null
    userId: number | null
    betTon: Decimal | null
    winTon: Decimal | null
  }

  export type DoubleBetMinAggregateOutputType = {
    id: number | null
    roundId: number | null
    userId: number | null
    betTon: Decimal | null
    color: string | null
    winTon: Decimal | null
    createdAt: Date | null
  }

  export type DoubleBetMaxAggregateOutputType = {
    id: number | null
    roundId: number | null
    userId: number | null
    betTon: Decimal | null
    color: string | null
    winTon: Decimal | null
    createdAt: Date | null
  }

  export type DoubleBetCountAggregateOutputType = {
    id: number
    roundId: number
    userId: number
    betTon: number
    color: number
    winTon: number
    createdAt: number
    _all: number
  }


  export type DoubleBetAvgAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    winTon?: true
  }

  export type DoubleBetSumAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    winTon?: true
  }

  export type DoubleBetMinAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    color?: true
    winTon?: true
    createdAt?: true
  }

  export type DoubleBetMaxAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    color?: true
    winTon?: true
    createdAt?: true
  }

  export type DoubleBetCountAggregateInputType = {
    id?: true
    roundId?: true
    userId?: true
    betTon?: true
    color?: true
    winTon?: true
    createdAt?: true
    _all?: true
  }

  export type DoubleBetAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which DoubleBet to aggregate.
     */
    where?: DoubleBetWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DoubleBets to fetch.
     */
    orderBy?: DoubleBetOrderByWithRelationInput | DoubleBetOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: DoubleBetWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DoubleBets from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DoubleBets.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned DoubleBets
    **/
    _count?: true | DoubleBetCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: DoubleBetAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: DoubleBetSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: DoubleBetMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: DoubleBetMaxAggregateInputType
  }

  export type GetDoubleBetAggregateType<T extends DoubleBetAggregateArgs> = {
        [P in keyof T & keyof AggregateDoubleBet]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateDoubleBet[P]>
      : GetScalarType<T[P], AggregateDoubleBet[P]>
  }




  export type DoubleBetGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: DoubleBetWhereInput
    orderBy?: DoubleBetOrderByWithAggregationInput | DoubleBetOrderByWithAggregationInput[]
    by: DoubleBetScalarFieldEnum[] | DoubleBetScalarFieldEnum
    having?: DoubleBetScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: DoubleBetCountAggregateInputType | true
    _avg?: DoubleBetAvgAggregateInputType
    _sum?: DoubleBetSumAggregateInputType
    _min?: DoubleBetMinAggregateInputType
    _max?: DoubleBetMaxAggregateInputType
  }

  export type DoubleBetGroupByOutputType = {
    id: number
    roundId: number
    userId: number
    betTon: Decimal
    color: string
    winTon: Decimal | null
    createdAt: Date
    _count: DoubleBetCountAggregateOutputType | null
    _avg: DoubleBetAvgAggregateOutputType | null
    _sum: DoubleBetSumAggregateOutputType | null
    _min: DoubleBetMinAggregateOutputType | null
    _max: DoubleBetMaxAggregateOutputType | null
  }

  type GetDoubleBetGroupByPayload<T extends DoubleBetGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<DoubleBetGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof DoubleBetGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], DoubleBetGroupByOutputType[P]>
            : GetScalarType<T[P], DoubleBetGroupByOutputType[P]>
        }
      >
    >


  export type DoubleBetSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    roundId?: boolean
    userId?: boolean
    betTon?: boolean
    color?: boolean
    winTon?: boolean
    createdAt?: boolean
    round?: boolean | DoubleRoundDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["doubleBet"]>

  export type DoubleBetSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    roundId?: boolean
    userId?: boolean
    betTon?: boolean
    color?: boolean
    winTon?: boolean
    createdAt?: boolean
    round?: boolean | DoubleRoundDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["doubleBet"]>

  export type DoubleBetSelectScalar = {
    id?: boolean
    roundId?: boolean
    userId?: boolean
    betTon?: boolean
    color?: boolean
    winTon?: boolean
    createdAt?: boolean
  }

  export type DoubleBetInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    round?: boolean | DoubleRoundDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type DoubleBetIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    round?: boolean | DoubleRoundDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $DoubleBetPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "DoubleBet"
    objects: {
      round: Prisma.$DoubleRoundPayload<ExtArgs>
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      roundId: number
      userId: number
      betTon: Prisma.Decimal
      color: string
      winTon: Prisma.Decimal | null
      createdAt: Date
    }, ExtArgs["result"]["doubleBet"]>
    composites: {}
  }

  type DoubleBetGetPayload<S extends boolean | null | undefined | DoubleBetDefaultArgs> = $Result.GetResult<Prisma.$DoubleBetPayload, S>

  type DoubleBetCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<DoubleBetFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: DoubleBetCountAggregateInputType | true
    }

  export interface DoubleBetDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['DoubleBet'], meta: { name: 'DoubleBet' } }
    /**
     * Find zero or one DoubleBet that matches the filter.
     * @param {DoubleBetFindUniqueArgs} args - Arguments to find a DoubleBet
     * @example
     * // Get one DoubleBet
     * const doubleBet = await prisma.doubleBet.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends DoubleBetFindUniqueArgs>(args: SelectSubset<T, DoubleBetFindUniqueArgs<ExtArgs>>): Prisma__DoubleBetClient<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one DoubleBet that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {DoubleBetFindUniqueOrThrowArgs} args - Arguments to find a DoubleBet
     * @example
     * // Get one DoubleBet
     * const doubleBet = await prisma.doubleBet.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends DoubleBetFindUniqueOrThrowArgs>(args: SelectSubset<T, DoubleBetFindUniqueOrThrowArgs<ExtArgs>>): Prisma__DoubleBetClient<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first DoubleBet that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleBetFindFirstArgs} args - Arguments to find a DoubleBet
     * @example
     * // Get one DoubleBet
     * const doubleBet = await prisma.doubleBet.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends DoubleBetFindFirstArgs>(args?: SelectSubset<T, DoubleBetFindFirstArgs<ExtArgs>>): Prisma__DoubleBetClient<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first DoubleBet that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleBetFindFirstOrThrowArgs} args - Arguments to find a DoubleBet
     * @example
     * // Get one DoubleBet
     * const doubleBet = await prisma.doubleBet.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends DoubleBetFindFirstOrThrowArgs>(args?: SelectSubset<T, DoubleBetFindFirstOrThrowArgs<ExtArgs>>): Prisma__DoubleBetClient<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more DoubleBets that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleBetFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all DoubleBets
     * const doubleBets = await prisma.doubleBet.findMany()
     * 
     * // Get first 10 DoubleBets
     * const doubleBets = await prisma.doubleBet.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const doubleBetWithIdOnly = await prisma.doubleBet.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends DoubleBetFindManyArgs>(args?: SelectSubset<T, DoubleBetFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a DoubleBet.
     * @param {DoubleBetCreateArgs} args - Arguments to create a DoubleBet.
     * @example
     * // Create one DoubleBet
     * const DoubleBet = await prisma.doubleBet.create({
     *   data: {
     *     // ... data to create a DoubleBet
     *   }
     * })
     * 
     */
    create<T extends DoubleBetCreateArgs>(args: SelectSubset<T, DoubleBetCreateArgs<ExtArgs>>): Prisma__DoubleBetClient<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many DoubleBets.
     * @param {DoubleBetCreateManyArgs} args - Arguments to create many DoubleBets.
     * @example
     * // Create many DoubleBets
     * const doubleBet = await prisma.doubleBet.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends DoubleBetCreateManyArgs>(args?: SelectSubset<T, DoubleBetCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many DoubleBets and returns the data saved in the database.
     * @param {DoubleBetCreateManyAndReturnArgs} args - Arguments to create many DoubleBets.
     * @example
     * // Create many DoubleBets
     * const doubleBet = await prisma.doubleBet.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many DoubleBets and only return the `id`
     * const doubleBetWithIdOnly = await prisma.doubleBet.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends DoubleBetCreateManyAndReturnArgs>(args?: SelectSubset<T, DoubleBetCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a DoubleBet.
     * @param {DoubleBetDeleteArgs} args - Arguments to delete one DoubleBet.
     * @example
     * // Delete one DoubleBet
     * const DoubleBet = await prisma.doubleBet.delete({
     *   where: {
     *     // ... filter to delete one DoubleBet
     *   }
     * })
     * 
     */
    delete<T extends DoubleBetDeleteArgs>(args: SelectSubset<T, DoubleBetDeleteArgs<ExtArgs>>): Prisma__DoubleBetClient<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one DoubleBet.
     * @param {DoubleBetUpdateArgs} args - Arguments to update one DoubleBet.
     * @example
     * // Update one DoubleBet
     * const doubleBet = await prisma.doubleBet.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends DoubleBetUpdateArgs>(args: SelectSubset<T, DoubleBetUpdateArgs<ExtArgs>>): Prisma__DoubleBetClient<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more DoubleBets.
     * @param {DoubleBetDeleteManyArgs} args - Arguments to filter DoubleBets to delete.
     * @example
     * // Delete a few DoubleBets
     * const { count } = await prisma.doubleBet.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends DoubleBetDeleteManyArgs>(args?: SelectSubset<T, DoubleBetDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more DoubleBets.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleBetUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many DoubleBets
     * const doubleBet = await prisma.doubleBet.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends DoubleBetUpdateManyArgs>(args: SelectSubset<T, DoubleBetUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one DoubleBet.
     * @param {DoubleBetUpsertArgs} args - Arguments to update or create a DoubleBet.
     * @example
     * // Update or create a DoubleBet
     * const doubleBet = await prisma.doubleBet.upsert({
     *   create: {
     *     // ... data to create a DoubleBet
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the DoubleBet we want to update
     *   }
     * })
     */
    upsert<T extends DoubleBetUpsertArgs>(args: SelectSubset<T, DoubleBetUpsertArgs<ExtArgs>>): Prisma__DoubleBetClient<$Result.GetResult<Prisma.$DoubleBetPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of DoubleBets.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleBetCountArgs} args - Arguments to filter DoubleBets to count.
     * @example
     * // Count the number of DoubleBets
     * const count = await prisma.doubleBet.count({
     *   where: {
     *     // ... the filter for the DoubleBets we want to count
     *   }
     * })
    **/
    count<T extends DoubleBetCountArgs>(
      args?: Subset<T, DoubleBetCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], DoubleBetCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a DoubleBet.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleBetAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends DoubleBetAggregateArgs>(args: Subset<T, DoubleBetAggregateArgs>): Prisma.PrismaPromise<GetDoubleBetAggregateType<T>>

    /**
     * Group by DoubleBet.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DoubleBetGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends DoubleBetGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: DoubleBetGroupByArgs['orderBy'] }
        : { orderBy?: DoubleBetGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, DoubleBetGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDoubleBetGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the DoubleBet model
   */
  readonly fields: DoubleBetFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for DoubleBet.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__DoubleBetClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    round<T extends DoubleRoundDefaultArgs<ExtArgs> = {}>(args?: Subset<T, DoubleRoundDefaultArgs<ExtArgs>>): Prisma__DoubleRoundClient<$Result.GetResult<Prisma.$DoubleRoundPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the DoubleBet model
   */ 
  interface DoubleBetFieldRefs {
    readonly id: FieldRef<"DoubleBet", 'Int'>
    readonly roundId: FieldRef<"DoubleBet", 'Int'>
    readonly userId: FieldRef<"DoubleBet", 'Int'>
    readonly betTon: FieldRef<"DoubleBet", 'Decimal'>
    readonly color: FieldRef<"DoubleBet", 'String'>
    readonly winTon: FieldRef<"DoubleBet", 'Decimal'>
    readonly createdAt: FieldRef<"DoubleBet", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * DoubleBet findUnique
   */
  export type DoubleBetFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * Filter, which DoubleBet to fetch.
     */
    where: DoubleBetWhereUniqueInput
  }

  /**
   * DoubleBet findUniqueOrThrow
   */
  export type DoubleBetFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * Filter, which DoubleBet to fetch.
     */
    where: DoubleBetWhereUniqueInput
  }

  /**
   * DoubleBet findFirst
   */
  export type DoubleBetFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * Filter, which DoubleBet to fetch.
     */
    where?: DoubleBetWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DoubleBets to fetch.
     */
    orderBy?: DoubleBetOrderByWithRelationInput | DoubleBetOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for DoubleBets.
     */
    cursor?: DoubleBetWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DoubleBets from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DoubleBets.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of DoubleBets.
     */
    distinct?: DoubleBetScalarFieldEnum | DoubleBetScalarFieldEnum[]
  }

  /**
   * DoubleBet findFirstOrThrow
   */
  export type DoubleBetFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * Filter, which DoubleBet to fetch.
     */
    where?: DoubleBetWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DoubleBets to fetch.
     */
    orderBy?: DoubleBetOrderByWithRelationInput | DoubleBetOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for DoubleBets.
     */
    cursor?: DoubleBetWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DoubleBets from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DoubleBets.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of DoubleBets.
     */
    distinct?: DoubleBetScalarFieldEnum | DoubleBetScalarFieldEnum[]
  }

  /**
   * DoubleBet findMany
   */
  export type DoubleBetFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * Filter, which DoubleBets to fetch.
     */
    where?: DoubleBetWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DoubleBets to fetch.
     */
    orderBy?: DoubleBetOrderByWithRelationInput | DoubleBetOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing DoubleBets.
     */
    cursor?: DoubleBetWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DoubleBets from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DoubleBets.
     */
    skip?: number
    distinct?: DoubleBetScalarFieldEnum | DoubleBetScalarFieldEnum[]
  }

  /**
   * DoubleBet create
   */
  export type DoubleBetCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * The data needed to create a DoubleBet.
     */
    data: XOR<DoubleBetCreateInput, DoubleBetUncheckedCreateInput>
  }

  /**
   * DoubleBet createMany
   */
  export type DoubleBetCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many DoubleBets.
     */
    data: DoubleBetCreateManyInput | DoubleBetCreateManyInput[]
  }

  /**
   * DoubleBet createManyAndReturn
   */
  export type DoubleBetCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many DoubleBets.
     */
    data: DoubleBetCreateManyInput | DoubleBetCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * DoubleBet update
   */
  export type DoubleBetUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * The data needed to update a DoubleBet.
     */
    data: XOR<DoubleBetUpdateInput, DoubleBetUncheckedUpdateInput>
    /**
     * Choose, which DoubleBet to update.
     */
    where: DoubleBetWhereUniqueInput
  }

  /**
   * DoubleBet updateMany
   */
  export type DoubleBetUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update DoubleBets.
     */
    data: XOR<DoubleBetUpdateManyMutationInput, DoubleBetUncheckedUpdateManyInput>
    /**
     * Filter which DoubleBets to update
     */
    where?: DoubleBetWhereInput
  }

  /**
   * DoubleBet upsert
   */
  export type DoubleBetUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * The filter to search for the DoubleBet to update in case it exists.
     */
    where: DoubleBetWhereUniqueInput
    /**
     * In case the DoubleBet found by the `where` argument doesn't exist, create a new DoubleBet with this data.
     */
    create: XOR<DoubleBetCreateInput, DoubleBetUncheckedCreateInput>
    /**
     * In case the DoubleBet was found with the provided `where` argument, update it with this data.
     */
    update: XOR<DoubleBetUpdateInput, DoubleBetUncheckedUpdateInput>
  }

  /**
   * DoubleBet delete
   */
  export type DoubleBetDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
    /**
     * Filter which DoubleBet to delete.
     */
    where: DoubleBetWhereUniqueInput
  }

  /**
   * DoubleBet deleteMany
   */
  export type DoubleBetDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which DoubleBets to delete
     */
    where?: DoubleBetWhereInput
  }

  /**
   * DoubleBet without action
   */
  export type DoubleBetDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DoubleBet
     */
    select?: DoubleBetSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: DoubleBetInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    telegramId: 'telegramId',
    username: 'username',
    balanceTon: 'balanceTon',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const PriceScalarFieldEnum: {
    modelKey: 'modelKey',
    minTon: 'minTon',
    updatedAt: 'updatedAt'
  };

  export type PriceScalarFieldEnum = (typeof PriceScalarFieldEnum)[keyof typeof PriceScalarFieldEnum]


  export const TransactionScalarFieldEnum: {
    id: 'id',
    kind: 'kind',
    amountTon: 'amountTon',
    ref: 'ref',
    userId: 'userId',
    createdAt: 'createdAt'
  };

  export type TransactionScalarFieldEnum = (typeof TransactionScalarFieldEnum)[keyof typeof TransactionScalarFieldEnum]


  export const CoinflipRoomScalarFieldEnum: {
    id: 'id',
    stakeTon: 'stakeTon',
    playerAId: 'playerAId',
    playerBId: 'playerBId',
    seedA: 'seedA',
    seedB: 'seedB',
    winnerId: 'winnerId',
    status: 'status',
    createdAt: 'createdAt',
    finishedAt: 'finishedAt'
  };

  export type CoinflipRoomScalarFieldEnum = (typeof CoinflipRoomScalarFieldEnum)[keyof typeof CoinflipRoomScalarFieldEnum]


  export const CrashRoundScalarFieldEnum: {
    id: 'id',
    multiplier: 'multiplier',
    seed: 'seed',
    status: 'status',
    createdAt: 'createdAt',
    crashedAt: 'crashedAt'
  };

  export type CrashRoundScalarFieldEnum = (typeof CrashRoundScalarFieldEnum)[keyof typeof CrashRoundScalarFieldEnum]


  export const CrashBetScalarFieldEnum: {
    id: 'id',
    roundId: 'roundId',
    userId: 'userId',
    betTon: 'betTon',
    cashoutAt: 'cashoutAt',
    winTon: 'winTon',
    createdAt: 'createdAt'
  };

  export type CrashBetScalarFieldEnum = (typeof CrashBetScalarFieldEnum)[keyof typeof CrashBetScalarFieldEnum]


  export const DoubleRoundScalarFieldEnum: {
    id: 'id',
    result: 'result',
    seed: 'seed',
    createdAt: 'createdAt'
  };

  export type DoubleRoundScalarFieldEnum = (typeof DoubleRoundScalarFieldEnum)[keyof typeof DoubleRoundScalarFieldEnum]


  export const DoubleBetScalarFieldEnum: {
    id: 'id',
    roundId: 'roundId',
    userId: 'userId',
    betTon: 'betTon',
    color: 'color',
    winTon: 'winTon',
    createdAt: 'createdAt'
  };

  export type DoubleBetScalarFieldEnum = (typeof DoubleBetScalarFieldEnum)[keyof typeof DoubleBetScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references 
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'Decimal'
   */
  export type DecimalFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Decimal'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: IntFilter<"User"> | number
    telegramId?: StringFilter<"User"> | string
    username?: StringNullableFilter<"User"> | string | null
    balanceTon?: DecimalFilter<"User"> | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    transactions?: TransactionListRelationFilter
    coinflipsA?: CoinflipRoomListRelationFilter
    coinflipsB?: CoinflipRoomListRelationFilter
    crashBets?: CrashBetListRelationFilter
    doubleBets?: DoubleBetListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    telegramId?: SortOrder
    username?: SortOrderInput | SortOrder
    balanceTon?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    transactions?: TransactionOrderByRelationAggregateInput
    coinflipsA?: CoinflipRoomOrderByRelationAggregateInput
    coinflipsB?: CoinflipRoomOrderByRelationAggregateInput
    crashBets?: CrashBetOrderByRelationAggregateInput
    doubleBets?: DoubleBetOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    telegramId?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    username?: StringNullableFilter<"User"> | string | null
    balanceTon?: DecimalFilter<"User"> | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    transactions?: TransactionListRelationFilter
    coinflipsA?: CoinflipRoomListRelationFilter
    coinflipsB?: CoinflipRoomListRelationFilter
    crashBets?: CrashBetListRelationFilter
    doubleBets?: DoubleBetListRelationFilter
  }, "id" | "telegramId">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    telegramId?: SortOrder
    username?: SortOrderInput | SortOrder
    balanceTon?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _avg?: UserAvgOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
    _sum?: UserSumOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"User"> | number
    telegramId?: StringWithAggregatesFilter<"User"> | string
    username?: StringNullableWithAggregatesFilter<"User"> | string | null
    balanceTon?: DecimalWithAggregatesFilter<"User"> | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type PriceWhereInput = {
    AND?: PriceWhereInput | PriceWhereInput[]
    OR?: PriceWhereInput[]
    NOT?: PriceWhereInput | PriceWhereInput[]
    modelKey?: StringFilter<"Price"> | string
    minTon?: DecimalFilter<"Price"> | Decimal | DecimalJsLike | number | string
    updatedAt?: DateTimeFilter<"Price"> | Date | string
  }

  export type PriceOrderByWithRelationInput = {
    modelKey?: SortOrder
    minTon?: SortOrder
    updatedAt?: SortOrder
  }

  export type PriceWhereUniqueInput = Prisma.AtLeast<{
    modelKey?: string
    AND?: PriceWhereInput | PriceWhereInput[]
    OR?: PriceWhereInput[]
    NOT?: PriceWhereInput | PriceWhereInput[]
    minTon?: DecimalFilter<"Price"> | Decimal | DecimalJsLike | number | string
    updatedAt?: DateTimeFilter<"Price"> | Date | string
  }, "modelKey">

  export type PriceOrderByWithAggregationInput = {
    modelKey?: SortOrder
    minTon?: SortOrder
    updatedAt?: SortOrder
    _count?: PriceCountOrderByAggregateInput
    _avg?: PriceAvgOrderByAggregateInput
    _max?: PriceMaxOrderByAggregateInput
    _min?: PriceMinOrderByAggregateInput
    _sum?: PriceSumOrderByAggregateInput
  }

  export type PriceScalarWhereWithAggregatesInput = {
    AND?: PriceScalarWhereWithAggregatesInput | PriceScalarWhereWithAggregatesInput[]
    OR?: PriceScalarWhereWithAggregatesInput[]
    NOT?: PriceScalarWhereWithAggregatesInput | PriceScalarWhereWithAggregatesInput[]
    modelKey?: StringWithAggregatesFilter<"Price"> | string
    minTon?: DecimalWithAggregatesFilter<"Price"> | Decimal | DecimalJsLike | number | string
    updatedAt?: DateTimeWithAggregatesFilter<"Price"> | Date | string
  }

  export type TransactionWhereInput = {
    AND?: TransactionWhereInput | TransactionWhereInput[]
    OR?: TransactionWhereInput[]
    NOT?: TransactionWhereInput | TransactionWhereInput[]
    id?: IntFilter<"Transaction"> | number
    kind?: StringFilter<"Transaction"> | string
    amountTon?: DecimalFilter<"Transaction"> | Decimal | DecimalJsLike | number | string
    ref?: StringNullableFilter<"Transaction"> | string | null
    userId?: IntNullableFilter<"Transaction"> | number | null
    createdAt?: DateTimeFilter<"Transaction"> | Date | string
    user?: XOR<UserNullableRelationFilter, UserWhereInput> | null
  }

  export type TransactionOrderByWithRelationInput = {
    id?: SortOrder
    kind?: SortOrder
    amountTon?: SortOrder
    ref?: SortOrderInput | SortOrder
    userId?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type TransactionWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: TransactionWhereInput | TransactionWhereInput[]
    OR?: TransactionWhereInput[]
    NOT?: TransactionWhereInput | TransactionWhereInput[]
    kind?: StringFilter<"Transaction"> | string
    amountTon?: DecimalFilter<"Transaction"> | Decimal | DecimalJsLike | number | string
    ref?: StringNullableFilter<"Transaction"> | string | null
    userId?: IntNullableFilter<"Transaction"> | number | null
    createdAt?: DateTimeFilter<"Transaction"> | Date | string
    user?: XOR<UserNullableRelationFilter, UserWhereInput> | null
  }, "id">

  export type TransactionOrderByWithAggregationInput = {
    id?: SortOrder
    kind?: SortOrder
    amountTon?: SortOrder
    ref?: SortOrderInput | SortOrder
    userId?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    _count?: TransactionCountOrderByAggregateInput
    _avg?: TransactionAvgOrderByAggregateInput
    _max?: TransactionMaxOrderByAggregateInput
    _min?: TransactionMinOrderByAggregateInput
    _sum?: TransactionSumOrderByAggregateInput
  }

  export type TransactionScalarWhereWithAggregatesInput = {
    AND?: TransactionScalarWhereWithAggregatesInput | TransactionScalarWhereWithAggregatesInput[]
    OR?: TransactionScalarWhereWithAggregatesInput[]
    NOT?: TransactionScalarWhereWithAggregatesInput | TransactionScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Transaction"> | number
    kind?: StringWithAggregatesFilter<"Transaction"> | string
    amountTon?: DecimalWithAggregatesFilter<"Transaction"> | Decimal | DecimalJsLike | number | string
    ref?: StringNullableWithAggregatesFilter<"Transaction"> | string | null
    userId?: IntNullableWithAggregatesFilter<"Transaction"> | number | null
    createdAt?: DateTimeWithAggregatesFilter<"Transaction"> | Date | string
  }

  export type CoinflipRoomWhereInput = {
    AND?: CoinflipRoomWhereInput | CoinflipRoomWhereInput[]
    OR?: CoinflipRoomWhereInput[]
    NOT?: CoinflipRoomWhereInput | CoinflipRoomWhereInput[]
    id?: IntFilter<"CoinflipRoom"> | number
    stakeTon?: DecimalFilter<"CoinflipRoom"> | Decimal | DecimalJsLike | number | string
    playerAId?: IntFilter<"CoinflipRoom"> | number
    playerBId?: IntNullableFilter<"CoinflipRoom"> | number | null
    seedA?: StringNullableFilter<"CoinflipRoom"> | string | null
    seedB?: StringNullableFilter<"CoinflipRoom"> | string | null
    winnerId?: IntNullableFilter<"CoinflipRoom"> | number | null
    status?: StringFilter<"CoinflipRoom"> | string
    createdAt?: DateTimeFilter<"CoinflipRoom"> | Date | string
    finishedAt?: DateTimeNullableFilter<"CoinflipRoom"> | Date | string | null
    playerA?: XOR<UserRelationFilter, UserWhereInput>
    playerB?: XOR<UserNullableRelationFilter, UserWhereInput> | null
  }

  export type CoinflipRoomOrderByWithRelationInput = {
    id?: SortOrder
    stakeTon?: SortOrder
    playerAId?: SortOrder
    playerBId?: SortOrderInput | SortOrder
    seedA?: SortOrderInput | SortOrder
    seedB?: SortOrderInput | SortOrder
    winnerId?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrderInput | SortOrder
    playerA?: UserOrderByWithRelationInput
    playerB?: UserOrderByWithRelationInput
  }

  export type CoinflipRoomWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: CoinflipRoomWhereInput | CoinflipRoomWhereInput[]
    OR?: CoinflipRoomWhereInput[]
    NOT?: CoinflipRoomWhereInput | CoinflipRoomWhereInput[]
    stakeTon?: DecimalFilter<"CoinflipRoom"> | Decimal | DecimalJsLike | number | string
    playerAId?: IntFilter<"CoinflipRoom"> | number
    playerBId?: IntNullableFilter<"CoinflipRoom"> | number | null
    seedA?: StringNullableFilter<"CoinflipRoom"> | string | null
    seedB?: StringNullableFilter<"CoinflipRoom"> | string | null
    winnerId?: IntNullableFilter<"CoinflipRoom"> | number | null
    status?: StringFilter<"CoinflipRoom"> | string
    createdAt?: DateTimeFilter<"CoinflipRoom"> | Date | string
    finishedAt?: DateTimeNullableFilter<"CoinflipRoom"> | Date | string | null
    playerA?: XOR<UserRelationFilter, UserWhereInput>
    playerB?: XOR<UserNullableRelationFilter, UserWhereInput> | null
  }, "id">

  export type CoinflipRoomOrderByWithAggregationInput = {
    id?: SortOrder
    stakeTon?: SortOrder
    playerAId?: SortOrder
    playerBId?: SortOrderInput | SortOrder
    seedA?: SortOrderInput | SortOrder
    seedB?: SortOrderInput | SortOrder
    winnerId?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrderInput | SortOrder
    _count?: CoinflipRoomCountOrderByAggregateInput
    _avg?: CoinflipRoomAvgOrderByAggregateInput
    _max?: CoinflipRoomMaxOrderByAggregateInput
    _min?: CoinflipRoomMinOrderByAggregateInput
    _sum?: CoinflipRoomSumOrderByAggregateInput
  }

  export type CoinflipRoomScalarWhereWithAggregatesInput = {
    AND?: CoinflipRoomScalarWhereWithAggregatesInput | CoinflipRoomScalarWhereWithAggregatesInput[]
    OR?: CoinflipRoomScalarWhereWithAggregatesInput[]
    NOT?: CoinflipRoomScalarWhereWithAggregatesInput | CoinflipRoomScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"CoinflipRoom"> | number
    stakeTon?: DecimalWithAggregatesFilter<"CoinflipRoom"> | Decimal | DecimalJsLike | number | string
    playerAId?: IntWithAggregatesFilter<"CoinflipRoom"> | number
    playerBId?: IntNullableWithAggregatesFilter<"CoinflipRoom"> | number | null
    seedA?: StringNullableWithAggregatesFilter<"CoinflipRoom"> | string | null
    seedB?: StringNullableWithAggregatesFilter<"CoinflipRoom"> | string | null
    winnerId?: IntNullableWithAggregatesFilter<"CoinflipRoom"> | number | null
    status?: StringWithAggregatesFilter<"CoinflipRoom"> | string
    createdAt?: DateTimeWithAggregatesFilter<"CoinflipRoom"> | Date | string
    finishedAt?: DateTimeNullableWithAggregatesFilter<"CoinflipRoom"> | Date | string | null
  }

  export type CrashRoundWhereInput = {
    AND?: CrashRoundWhereInput | CrashRoundWhereInput[]
    OR?: CrashRoundWhereInput[]
    NOT?: CrashRoundWhereInput | CrashRoundWhereInput[]
    id?: IntFilter<"CrashRound"> | number
    multiplier?: DecimalFilter<"CrashRound"> | Decimal | DecimalJsLike | number | string
    seed?: StringFilter<"CrashRound"> | string
    status?: StringFilter<"CrashRound"> | string
    createdAt?: DateTimeFilter<"CrashRound"> | Date | string
    crashedAt?: DateTimeNullableFilter<"CrashRound"> | Date | string | null
    bets?: CrashBetListRelationFilter
  }

  export type CrashRoundOrderByWithRelationInput = {
    id?: SortOrder
    multiplier?: SortOrder
    seed?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    crashedAt?: SortOrderInput | SortOrder
    bets?: CrashBetOrderByRelationAggregateInput
  }

  export type CrashRoundWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: CrashRoundWhereInput | CrashRoundWhereInput[]
    OR?: CrashRoundWhereInput[]
    NOT?: CrashRoundWhereInput | CrashRoundWhereInput[]
    multiplier?: DecimalFilter<"CrashRound"> | Decimal | DecimalJsLike | number | string
    seed?: StringFilter<"CrashRound"> | string
    status?: StringFilter<"CrashRound"> | string
    createdAt?: DateTimeFilter<"CrashRound"> | Date | string
    crashedAt?: DateTimeNullableFilter<"CrashRound"> | Date | string | null
    bets?: CrashBetListRelationFilter
  }, "id">

  export type CrashRoundOrderByWithAggregationInput = {
    id?: SortOrder
    multiplier?: SortOrder
    seed?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    crashedAt?: SortOrderInput | SortOrder
    _count?: CrashRoundCountOrderByAggregateInput
    _avg?: CrashRoundAvgOrderByAggregateInput
    _max?: CrashRoundMaxOrderByAggregateInput
    _min?: CrashRoundMinOrderByAggregateInput
    _sum?: CrashRoundSumOrderByAggregateInput
  }

  export type CrashRoundScalarWhereWithAggregatesInput = {
    AND?: CrashRoundScalarWhereWithAggregatesInput | CrashRoundScalarWhereWithAggregatesInput[]
    OR?: CrashRoundScalarWhereWithAggregatesInput[]
    NOT?: CrashRoundScalarWhereWithAggregatesInput | CrashRoundScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"CrashRound"> | number
    multiplier?: DecimalWithAggregatesFilter<"CrashRound"> | Decimal | DecimalJsLike | number | string
    seed?: StringWithAggregatesFilter<"CrashRound"> | string
    status?: StringWithAggregatesFilter<"CrashRound"> | string
    createdAt?: DateTimeWithAggregatesFilter<"CrashRound"> | Date | string
    crashedAt?: DateTimeNullableWithAggregatesFilter<"CrashRound"> | Date | string | null
  }

  export type CrashBetWhereInput = {
    AND?: CrashBetWhereInput | CrashBetWhereInput[]
    OR?: CrashBetWhereInput[]
    NOT?: CrashBetWhereInput | CrashBetWhereInput[]
    id?: IntFilter<"CrashBet"> | number
    roundId?: IntFilter<"CrashBet"> | number
    userId?: IntFilter<"CrashBet"> | number
    betTon?: DecimalFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string
    cashoutAt?: DecimalNullableFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string | null
    winTon?: DecimalNullableFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFilter<"CrashBet"> | Date | string
    round?: XOR<CrashRoundRelationFilter, CrashRoundWhereInput>
    user?: XOR<UserRelationFilter, UserWhereInput>
  }

  export type CrashBetOrderByWithRelationInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    cashoutAt?: SortOrderInput | SortOrder
    winTon?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    round?: CrashRoundOrderByWithRelationInput
    user?: UserOrderByWithRelationInput
  }

  export type CrashBetWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    roundId_userId?: CrashBetRoundIdUserIdCompoundUniqueInput
    AND?: CrashBetWhereInput | CrashBetWhereInput[]
    OR?: CrashBetWhereInput[]
    NOT?: CrashBetWhereInput | CrashBetWhereInput[]
    roundId?: IntFilter<"CrashBet"> | number
    userId?: IntFilter<"CrashBet"> | number
    betTon?: DecimalFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string
    cashoutAt?: DecimalNullableFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string | null
    winTon?: DecimalNullableFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFilter<"CrashBet"> | Date | string
    round?: XOR<CrashRoundRelationFilter, CrashRoundWhereInput>
    user?: XOR<UserRelationFilter, UserWhereInput>
  }, "id" | "roundId_userId">

  export type CrashBetOrderByWithAggregationInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    cashoutAt?: SortOrderInput | SortOrder
    winTon?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    _count?: CrashBetCountOrderByAggregateInput
    _avg?: CrashBetAvgOrderByAggregateInput
    _max?: CrashBetMaxOrderByAggregateInput
    _min?: CrashBetMinOrderByAggregateInput
    _sum?: CrashBetSumOrderByAggregateInput
  }

  export type CrashBetScalarWhereWithAggregatesInput = {
    AND?: CrashBetScalarWhereWithAggregatesInput | CrashBetScalarWhereWithAggregatesInput[]
    OR?: CrashBetScalarWhereWithAggregatesInput[]
    NOT?: CrashBetScalarWhereWithAggregatesInput | CrashBetScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"CrashBet"> | number
    roundId?: IntWithAggregatesFilter<"CrashBet"> | number
    userId?: IntWithAggregatesFilter<"CrashBet"> | number
    betTon?: DecimalWithAggregatesFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string
    cashoutAt?: DecimalNullableWithAggregatesFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string | null
    winTon?: DecimalNullableWithAggregatesFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeWithAggregatesFilter<"CrashBet"> | Date | string
  }

  export type DoubleRoundWhereInput = {
    AND?: DoubleRoundWhereInput | DoubleRoundWhereInput[]
    OR?: DoubleRoundWhereInput[]
    NOT?: DoubleRoundWhereInput | DoubleRoundWhereInput[]
    id?: IntFilter<"DoubleRound"> | number
    result?: StringFilter<"DoubleRound"> | string
    seed?: StringFilter<"DoubleRound"> | string
    createdAt?: DateTimeFilter<"DoubleRound"> | Date | string
    bets?: DoubleBetListRelationFilter
  }

  export type DoubleRoundOrderByWithRelationInput = {
    id?: SortOrder
    result?: SortOrder
    seed?: SortOrder
    createdAt?: SortOrder
    bets?: DoubleBetOrderByRelationAggregateInput
  }

  export type DoubleRoundWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: DoubleRoundWhereInput | DoubleRoundWhereInput[]
    OR?: DoubleRoundWhereInput[]
    NOT?: DoubleRoundWhereInput | DoubleRoundWhereInput[]
    result?: StringFilter<"DoubleRound"> | string
    seed?: StringFilter<"DoubleRound"> | string
    createdAt?: DateTimeFilter<"DoubleRound"> | Date | string
    bets?: DoubleBetListRelationFilter
  }, "id">

  export type DoubleRoundOrderByWithAggregationInput = {
    id?: SortOrder
    result?: SortOrder
    seed?: SortOrder
    createdAt?: SortOrder
    _count?: DoubleRoundCountOrderByAggregateInput
    _avg?: DoubleRoundAvgOrderByAggregateInput
    _max?: DoubleRoundMaxOrderByAggregateInput
    _min?: DoubleRoundMinOrderByAggregateInput
    _sum?: DoubleRoundSumOrderByAggregateInput
  }

  export type DoubleRoundScalarWhereWithAggregatesInput = {
    AND?: DoubleRoundScalarWhereWithAggregatesInput | DoubleRoundScalarWhereWithAggregatesInput[]
    OR?: DoubleRoundScalarWhereWithAggregatesInput[]
    NOT?: DoubleRoundScalarWhereWithAggregatesInput | DoubleRoundScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"DoubleRound"> | number
    result?: StringWithAggregatesFilter<"DoubleRound"> | string
    seed?: StringWithAggregatesFilter<"DoubleRound"> | string
    createdAt?: DateTimeWithAggregatesFilter<"DoubleRound"> | Date | string
  }

  export type DoubleBetWhereInput = {
    AND?: DoubleBetWhereInput | DoubleBetWhereInput[]
    OR?: DoubleBetWhereInput[]
    NOT?: DoubleBetWhereInput | DoubleBetWhereInput[]
    id?: IntFilter<"DoubleBet"> | number
    roundId?: IntFilter<"DoubleBet"> | number
    userId?: IntFilter<"DoubleBet"> | number
    betTon?: DecimalFilter<"DoubleBet"> | Decimal | DecimalJsLike | number | string
    color?: StringFilter<"DoubleBet"> | string
    winTon?: DecimalNullableFilter<"DoubleBet"> | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFilter<"DoubleBet"> | Date | string
    round?: XOR<DoubleRoundRelationFilter, DoubleRoundWhereInput>
    user?: XOR<UserRelationFilter, UserWhereInput>
  }

  export type DoubleBetOrderByWithRelationInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    color?: SortOrder
    winTon?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    round?: DoubleRoundOrderByWithRelationInput
    user?: UserOrderByWithRelationInput
  }

  export type DoubleBetWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    roundId_userId_color?: DoubleBetRoundIdUserIdColorCompoundUniqueInput
    AND?: DoubleBetWhereInput | DoubleBetWhereInput[]
    OR?: DoubleBetWhereInput[]
    NOT?: DoubleBetWhereInput | DoubleBetWhereInput[]
    roundId?: IntFilter<"DoubleBet"> | number
    userId?: IntFilter<"DoubleBet"> | number
    betTon?: DecimalFilter<"DoubleBet"> | Decimal | DecimalJsLike | number | string
    color?: StringFilter<"DoubleBet"> | string
    winTon?: DecimalNullableFilter<"DoubleBet"> | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFilter<"DoubleBet"> | Date | string
    round?: XOR<DoubleRoundRelationFilter, DoubleRoundWhereInput>
    user?: XOR<UserRelationFilter, UserWhereInput>
  }, "id" | "roundId_userId_color">

  export type DoubleBetOrderByWithAggregationInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    color?: SortOrder
    winTon?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    _count?: DoubleBetCountOrderByAggregateInput
    _avg?: DoubleBetAvgOrderByAggregateInput
    _max?: DoubleBetMaxOrderByAggregateInput
    _min?: DoubleBetMinOrderByAggregateInput
    _sum?: DoubleBetSumOrderByAggregateInput
  }

  export type DoubleBetScalarWhereWithAggregatesInput = {
    AND?: DoubleBetScalarWhereWithAggregatesInput | DoubleBetScalarWhereWithAggregatesInput[]
    OR?: DoubleBetScalarWhereWithAggregatesInput[]
    NOT?: DoubleBetScalarWhereWithAggregatesInput | DoubleBetScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"DoubleBet"> | number
    roundId?: IntWithAggregatesFilter<"DoubleBet"> | number
    userId?: IntWithAggregatesFilter<"DoubleBet"> | number
    betTon?: DecimalWithAggregatesFilter<"DoubleBet"> | Decimal | DecimalJsLike | number | string
    color?: StringWithAggregatesFilter<"DoubleBet"> | string
    winTon?: DecimalNullableWithAggregatesFilter<"DoubleBet"> | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeWithAggregatesFilter<"DoubleBet"> | Date | string
  }

  export type UserCreateInput = {
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionCreateNestedManyWithoutUserInput
    coinflipsA?: CoinflipRoomCreateNestedManyWithoutPlayerAInput
    coinflipsB?: CoinflipRoomCreateNestedManyWithoutPlayerBInput
    crashBets?: CrashBetCreateNestedManyWithoutUserInput
    doubleBets?: DoubleBetCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: number
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionUncheckedCreateNestedManyWithoutUserInput
    coinflipsA?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerAInput
    coinflipsB?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerBInput
    crashBets?: CrashBetUncheckedCreateNestedManyWithoutUserInput
    doubleBets?: DoubleBetUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUpdateManyWithoutUserNestedInput
    coinflipsA?: CoinflipRoomUpdateManyWithoutPlayerANestedInput
    coinflipsB?: CoinflipRoomUpdateManyWithoutPlayerBNestedInput
    crashBets?: CrashBetUpdateManyWithoutUserNestedInput
    doubleBets?: DoubleBetUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUncheckedUpdateManyWithoutUserNestedInput
    coinflipsA?: CoinflipRoomUncheckedUpdateManyWithoutPlayerANestedInput
    coinflipsB?: CoinflipRoomUncheckedUpdateManyWithoutPlayerBNestedInput
    crashBets?: CrashBetUncheckedUpdateManyWithoutUserNestedInput
    doubleBets?: DoubleBetUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: number
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PriceCreateInput = {
    modelKey: string
    minTon: Decimal | DecimalJsLike | number | string
    updatedAt?: Date | string
  }

  export type PriceUncheckedCreateInput = {
    modelKey: string
    minTon: Decimal | DecimalJsLike | number | string
    updatedAt?: Date | string
  }

  export type PriceUpdateInput = {
    modelKey?: StringFieldUpdateOperationsInput | string
    minTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PriceUncheckedUpdateInput = {
    modelKey?: StringFieldUpdateOperationsInput | string
    minTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PriceCreateManyInput = {
    modelKey: string
    minTon: Decimal | DecimalJsLike | number | string
    updatedAt?: Date | string
  }

  export type PriceUpdateManyMutationInput = {
    modelKey?: StringFieldUpdateOperationsInput | string
    minTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PriceUncheckedUpdateManyInput = {
    modelKey?: StringFieldUpdateOperationsInput | string
    minTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TransactionCreateInput = {
    kind: string
    amountTon: Decimal | DecimalJsLike | number | string
    ref?: string | null
    createdAt?: Date | string
    user?: UserCreateNestedOneWithoutTransactionsInput
  }

  export type TransactionUncheckedCreateInput = {
    id?: number
    kind: string
    amountTon: Decimal | DecimalJsLike | number | string
    ref?: string | null
    userId?: number | null
    createdAt?: Date | string
  }

  export type TransactionUpdateInput = {
    kind?: StringFieldUpdateOperationsInput | string
    amountTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    ref?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneWithoutTransactionsNestedInput
  }

  export type TransactionUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    kind?: StringFieldUpdateOperationsInput | string
    amountTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    ref?: NullableStringFieldUpdateOperationsInput | string | null
    userId?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TransactionCreateManyInput = {
    id?: number
    kind: string
    amountTon: Decimal | DecimalJsLike | number | string
    ref?: string | null
    userId?: number | null
    createdAt?: Date | string
  }

  export type TransactionUpdateManyMutationInput = {
    kind?: StringFieldUpdateOperationsInput | string
    amountTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    ref?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TransactionUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    kind?: StringFieldUpdateOperationsInput | string
    amountTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    ref?: NullableStringFieldUpdateOperationsInput | string | null
    userId?: NullableIntFieldUpdateOperationsInput | number | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CoinflipRoomCreateInput = {
    stakeTon: Decimal | DecimalJsLike | number | string
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
    playerA: UserCreateNestedOneWithoutCoinflipsAInput
    playerB?: UserCreateNestedOneWithoutCoinflipsBInput
  }

  export type CoinflipRoomUncheckedCreateInput = {
    id?: number
    stakeTon: Decimal | DecimalJsLike | number | string
    playerAId: number
    playerBId?: number | null
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
  }

  export type CoinflipRoomUpdateInput = {
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    playerA?: UserUpdateOneRequiredWithoutCoinflipsANestedInput
    playerB?: UserUpdateOneWithoutCoinflipsBNestedInput
  }

  export type CoinflipRoomUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    playerAId?: IntFieldUpdateOperationsInput | number
    playerBId?: NullableIntFieldUpdateOperationsInput | number | null
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CoinflipRoomCreateManyInput = {
    id?: number
    stakeTon: Decimal | DecimalJsLike | number | string
    playerAId: number
    playerBId?: number | null
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
  }

  export type CoinflipRoomUpdateManyMutationInput = {
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CoinflipRoomUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    playerAId?: IntFieldUpdateOperationsInput | number
    playerBId?: NullableIntFieldUpdateOperationsInput | number | null
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrashRoundCreateInput = {
    multiplier: Decimal | DecimalJsLike | number | string
    seed: string
    status?: string
    createdAt?: Date | string
    crashedAt?: Date | string | null
    bets?: CrashBetCreateNestedManyWithoutRoundInput
  }

  export type CrashRoundUncheckedCreateInput = {
    id?: number
    multiplier: Decimal | DecimalJsLike | number | string
    seed: string
    status?: string
    createdAt?: Date | string
    crashedAt?: Date | string | null
    bets?: CrashBetUncheckedCreateNestedManyWithoutRoundInput
  }

  export type CrashRoundUpdateInput = {
    multiplier?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seed?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    crashedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    bets?: CrashBetUpdateManyWithoutRoundNestedInput
  }

  export type CrashRoundUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    multiplier?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seed?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    crashedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    bets?: CrashBetUncheckedUpdateManyWithoutRoundNestedInput
  }

  export type CrashRoundCreateManyInput = {
    id?: number
    multiplier: Decimal | DecimalJsLike | number | string
    seed: string
    status?: string
    createdAt?: Date | string
    crashedAt?: Date | string | null
  }

  export type CrashRoundUpdateManyMutationInput = {
    multiplier?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seed?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    crashedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrashRoundUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    multiplier?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seed?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    crashedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrashBetCreateInput = {
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
    round: CrashRoundCreateNestedOneWithoutBetsInput
    user: UserCreateNestedOneWithoutCrashBetsInput
  }

  export type CrashBetUncheckedCreateInput = {
    id?: number
    roundId: number
    userId: number
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type CrashBetUpdateInput = {
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    round?: CrashRoundUpdateOneRequiredWithoutBetsNestedInput
    user?: UserUpdateOneRequiredWithoutCrashBetsNestedInput
  }

  export type CrashBetUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    roundId?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CrashBetCreateManyInput = {
    id?: number
    roundId: number
    userId: number
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type CrashBetUpdateManyMutationInput = {
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CrashBetUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    roundId?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleRoundCreateInput = {
    result: string
    seed: string
    createdAt?: Date | string
    bets?: DoubleBetCreateNestedManyWithoutRoundInput
  }

  export type DoubleRoundUncheckedCreateInput = {
    id?: number
    result: string
    seed: string
    createdAt?: Date | string
    bets?: DoubleBetUncheckedCreateNestedManyWithoutRoundInput
  }

  export type DoubleRoundUpdateInput = {
    result?: StringFieldUpdateOperationsInput | string
    seed?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    bets?: DoubleBetUpdateManyWithoutRoundNestedInput
  }

  export type DoubleRoundUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    result?: StringFieldUpdateOperationsInput | string
    seed?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    bets?: DoubleBetUncheckedUpdateManyWithoutRoundNestedInput
  }

  export type DoubleRoundCreateManyInput = {
    id?: number
    result: string
    seed: string
    createdAt?: Date | string
  }

  export type DoubleRoundUpdateManyMutationInput = {
    result?: StringFieldUpdateOperationsInput | string
    seed?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleRoundUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    result?: StringFieldUpdateOperationsInput | string
    seed?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleBetCreateInput = {
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
    round: DoubleRoundCreateNestedOneWithoutBetsInput
    user: UserCreateNestedOneWithoutDoubleBetsInput
  }

  export type DoubleBetUncheckedCreateInput = {
    id?: number
    roundId: number
    userId: number
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type DoubleBetUpdateInput = {
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    round?: DoubleRoundUpdateOneRequiredWithoutBetsNestedInput
    user?: UserUpdateOneRequiredWithoutDoubleBetsNestedInput
  }

  export type DoubleBetUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    roundId?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleBetCreateManyInput = {
    id?: number
    roundId: number
    userId: number
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type DoubleBetUpdateManyMutationInput = {
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleBetUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    roundId?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DecimalFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    in?: Decimal[] | DecimalJsLike[] | number[] | string[]
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[]
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type TransactionListRelationFilter = {
    every?: TransactionWhereInput
    some?: TransactionWhereInput
    none?: TransactionWhereInput
  }

  export type CoinflipRoomListRelationFilter = {
    every?: CoinflipRoomWhereInput
    some?: CoinflipRoomWhereInput
    none?: CoinflipRoomWhereInput
  }

  export type CrashBetListRelationFilter = {
    every?: CrashBetWhereInput
    some?: CrashBetWhereInput
    none?: CrashBetWhereInput
  }

  export type DoubleBetListRelationFilter = {
    every?: DoubleBetWhereInput
    some?: DoubleBetWhereInput
    none?: DoubleBetWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type TransactionOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type CoinflipRoomOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type CrashBetOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type DoubleBetOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    telegramId?: SortOrder
    username?: SortOrder
    balanceTon?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserAvgOrderByAggregateInput = {
    id?: SortOrder
    balanceTon?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    telegramId?: SortOrder
    username?: SortOrder
    balanceTon?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    telegramId?: SortOrder
    username?: SortOrder
    balanceTon?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSumOrderByAggregateInput = {
    id?: SortOrder
    balanceTon?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DecimalWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    in?: Decimal[] | DecimalJsLike[] | number[] | string[]
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[]
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalWithAggregatesFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedDecimalFilter<$PrismaModel>
    _sum?: NestedDecimalFilter<$PrismaModel>
    _min?: NestedDecimalFilter<$PrismaModel>
    _max?: NestedDecimalFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type PriceCountOrderByAggregateInput = {
    modelKey?: SortOrder
    minTon?: SortOrder
    updatedAt?: SortOrder
  }

  export type PriceAvgOrderByAggregateInput = {
    minTon?: SortOrder
  }

  export type PriceMaxOrderByAggregateInput = {
    modelKey?: SortOrder
    minTon?: SortOrder
    updatedAt?: SortOrder
  }

  export type PriceMinOrderByAggregateInput = {
    modelKey?: SortOrder
    minTon?: SortOrder
    updatedAt?: SortOrder
  }

  export type PriceSumOrderByAggregateInput = {
    minTon?: SortOrder
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type UserNullableRelationFilter = {
    is?: UserWhereInput | null
    isNot?: UserWhereInput | null
  }

  export type TransactionCountOrderByAggregateInput = {
    id?: SortOrder
    kind?: SortOrder
    amountTon?: SortOrder
    ref?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
  }

  export type TransactionAvgOrderByAggregateInput = {
    id?: SortOrder
    amountTon?: SortOrder
    userId?: SortOrder
  }

  export type TransactionMaxOrderByAggregateInput = {
    id?: SortOrder
    kind?: SortOrder
    amountTon?: SortOrder
    ref?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
  }

  export type TransactionMinOrderByAggregateInput = {
    id?: SortOrder
    kind?: SortOrder
    amountTon?: SortOrder
    ref?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
  }

  export type TransactionSumOrderByAggregateInput = {
    id?: SortOrder
    amountTon?: SortOrder
    userId?: SortOrder
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type UserRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type CoinflipRoomCountOrderByAggregateInput = {
    id?: SortOrder
    stakeTon?: SortOrder
    playerAId?: SortOrder
    playerBId?: SortOrder
    seedA?: SortOrder
    seedB?: SortOrder
    winnerId?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrder
  }

  export type CoinflipRoomAvgOrderByAggregateInput = {
    id?: SortOrder
    stakeTon?: SortOrder
    playerAId?: SortOrder
    playerBId?: SortOrder
    winnerId?: SortOrder
  }

  export type CoinflipRoomMaxOrderByAggregateInput = {
    id?: SortOrder
    stakeTon?: SortOrder
    playerAId?: SortOrder
    playerBId?: SortOrder
    seedA?: SortOrder
    seedB?: SortOrder
    winnerId?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrder
  }

  export type CoinflipRoomMinOrderByAggregateInput = {
    id?: SortOrder
    stakeTon?: SortOrder
    playerAId?: SortOrder
    playerBId?: SortOrder
    seedA?: SortOrder
    seedB?: SortOrder
    winnerId?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrder
  }

  export type CoinflipRoomSumOrderByAggregateInput = {
    id?: SortOrder
    stakeTon?: SortOrder
    playerAId?: SortOrder
    playerBId?: SortOrder
    winnerId?: SortOrder
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type CrashRoundCountOrderByAggregateInput = {
    id?: SortOrder
    multiplier?: SortOrder
    seed?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    crashedAt?: SortOrder
  }

  export type CrashRoundAvgOrderByAggregateInput = {
    id?: SortOrder
    multiplier?: SortOrder
  }

  export type CrashRoundMaxOrderByAggregateInput = {
    id?: SortOrder
    multiplier?: SortOrder
    seed?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    crashedAt?: SortOrder
  }

  export type CrashRoundMinOrderByAggregateInput = {
    id?: SortOrder
    multiplier?: SortOrder
    seed?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    crashedAt?: SortOrder
  }

  export type CrashRoundSumOrderByAggregateInput = {
    id?: SortOrder
    multiplier?: SortOrder
  }

  export type DecimalNullableFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel> | null
    in?: Decimal[] | DecimalJsLike[] | number[] | string[] | null
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[] | null
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalNullableFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string | null
  }

  export type CrashRoundRelationFilter = {
    is?: CrashRoundWhereInput
    isNot?: CrashRoundWhereInput
  }

  export type CrashBetRoundIdUserIdCompoundUniqueInput = {
    roundId: number
    userId: number
  }

  export type CrashBetCountOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    cashoutAt?: SortOrder
    winTon?: SortOrder
    createdAt?: SortOrder
  }

  export type CrashBetAvgOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    cashoutAt?: SortOrder
    winTon?: SortOrder
  }

  export type CrashBetMaxOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    cashoutAt?: SortOrder
    winTon?: SortOrder
    createdAt?: SortOrder
  }

  export type CrashBetMinOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    cashoutAt?: SortOrder
    winTon?: SortOrder
    createdAt?: SortOrder
  }

  export type CrashBetSumOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    cashoutAt?: SortOrder
    winTon?: SortOrder
  }

  export type DecimalNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel> | null
    in?: Decimal[] | DecimalJsLike[] | number[] | string[] | null
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[] | null
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalNullableWithAggregatesFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedDecimalNullableFilter<$PrismaModel>
    _sum?: NestedDecimalNullableFilter<$PrismaModel>
    _min?: NestedDecimalNullableFilter<$PrismaModel>
    _max?: NestedDecimalNullableFilter<$PrismaModel>
  }

  export type DoubleRoundCountOrderByAggregateInput = {
    id?: SortOrder
    result?: SortOrder
    seed?: SortOrder
    createdAt?: SortOrder
  }

  export type DoubleRoundAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type DoubleRoundMaxOrderByAggregateInput = {
    id?: SortOrder
    result?: SortOrder
    seed?: SortOrder
    createdAt?: SortOrder
  }

  export type DoubleRoundMinOrderByAggregateInput = {
    id?: SortOrder
    result?: SortOrder
    seed?: SortOrder
    createdAt?: SortOrder
  }

  export type DoubleRoundSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type DoubleRoundRelationFilter = {
    is?: DoubleRoundWhereInput
    isNot?: DoubleRoundWhereInput
  }

  export type DoubleBetRoundIdUserIdColorCompoundUniqueInput = {
    roundId: number
    userId: number
    color: string
  }

  export type DoubleBetCountOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    color?: SortOrder
    winTon?: SortOrder
    createdAt?: SortOrder
  }

  export type DoubleBetAvgOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    winTon?: SortOrder
  }

  export type DoubleBetMaxOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    color?: SortOrder
    winTon?: SortOrder
    createdAt?: SortOrder
  }

  export type DoubleBetMinOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    color?: SortOrder
    winTon?: SortOrder
    createdAt?: SortOrder
  }

  export type DoubleBetSumOrderByAggregateInput = {
    id?: SortOrder
    roundId?: SortOrder
    userId?: SortOrder
    betTon?: SortOrder
    winTon?: SortOrder
  }

  export type TransactionCreateNestedManyWithoutUserInput = {
    create?: XOR<TransactionCreateWithoutUserInput, TransactionUncheckedCreateWithoutUserInput> | TransactionCreateWithoutUserInput[] | TransactionUncheckedCreateWithoutUserInput[]
    connectOrCreate?: TransactionCreateOrConnectWithoutUserInput | TransactionCreateOrConnectWithoutUserInput[]
    createMany?: TransactionCreateManyUserInputEnvelope
    connect?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
  }

  export type CoinflipRoomCreateNestedManyWithoutPlayerAInput = {
    create?: XOR<CoinflipRoomCreateWithoutPlayerAInput, CoinflipRoomUncheckedCreateWithoutPlayerAInput> | CoinflipRoomCreateWithoutPlayerAInput[] | CoinflipRoomUncheckedCreateWithoutPlayerAInput[]
    connectOrCreate?: CoinflipRoomCreateOrConnectWithoutPlayerAInput | CoinflipRoomCreateOrConnectWithoutPlayerAInput[]
    createMany?: CoinflipRoomCreateManyPlayerAInputEnvelope
    connect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
  }

  export type CoinflipRoomCreateNestedManyWithoutPlayerBInput = {
    create?: XOR<CoinflipRoomCreateWithoutPlayerBInput, CoinflipRoomUncheckedCreateWithoutPlayerBInput> | CoinflipRoomCreateWithoutPlayerBInput[] | CoinflipRoomUncheckedCreateWithoutPlayerBInput[]
    connectOrCreate?: CoinflipRoomCreateOrConnectWithoutPlayerBInput | CoinflipRoomCreateOrConnectWithoutPlayerBInput[]
    createMany?: CoinflipRoomCreateManyPlayerBInputEnvelope
    connect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
  }

  export type CrashBetCreateNestedManyWithoutUserInput = {
    create?: XOR<CrashBetCreateWithoutUserInput, CrashBetUncheckedCreateWithoutUserInput> | CrashBetCreateWithoutUserInput[] | CrashBetUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CrashBetCreateOrConnectWithoutUserInput | CrashBetCreateOrConnectWithoutUserInput[]
    createMany?: CrashBetCreateManyUserInputEnvelope
    connect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
  }

  export type DoubleBetCreateNestedManyWithoutUserInput = {
    create?: XOR<DoubleBetCreateWithoutUserInput, DoubleBetUncheckedCreateWithoutUserInput> | DoubleBetCreateWithoutUserInput[] | DoubleBetUncheckedCreateWithoutUserInput[]
    connectOrCreate?: DoubleBetCreateOrConnectWithoutUserInput | DoubleBetCreateOrConnectWithoutUserInput[]
    createMany?: DoubleBetCreateManyUserInputEnvelope
    connect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
  }

  export type TransactionUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<TransactionCreateWithoutUserInput, TransactionUncheckedCreateWithoutUserInput> | TransactionCreateWithoutUserInput[] | TransactionUncheckedCreateWithoutUserInput[]
    connectOrCreate?: TransactionCreateOrConnectWithoutUserInput | TransactionCreateOrConnectWithoutUserInput[]
    createMany?: TransactionCreateManyUserInputEnvelope
    connect?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
  }

  export type CoinflipRoomUncheckedCreateNestedManyWithoutPlayerAInput = {
    create?: XOR<CoinflipRoomCreateWithoutPlayerAInput, CoinflipRoomUncheckedCreateWithoutPlayerAInput> | CoinflipRoomCreateWithoutPlayerAInput[] | CoinflipRoomUncheckedCreateWithoutPlayerAInput[]
    connectOrCreate?: CoinflipRoomCreateOrConnectWithoutPlayerAInput | CoinflipRoomCreateOrConnectWithoutPlayerAInput[]
    createMany?: CoinflipRoomCreateManyPlayerAInputEnvelope
    connect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
  }

  export type CoinflipRoomUncheckedCreateNestedManyWithoutPlayerBInput = {
    create?: XOR<CoinflipRoomCreateWithoutPlayerBInput, CoinflipRoomUncheckedCreateWithoutPlayerBInput> | CoinflipRoomCreateWithoutPlayerBInput[] | CoinflipRoomUncheckedCreateWithoutPlayerBInput[]
    connectOrCreate?: CoinflipRoomCreateOrConnectWithoutPlayerBInput | CoinflipRoomCreateOrConnectWithoutPlayerBInput[]
    createMany?: CoinflipRoomCreateManyPlayerBInputEnvelope
    connect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
  }

  export type CrashBetUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<CrashBetCreateWithoutUserInput, CrashBetUncheckedCreateWithoutUserInput> | CrashBetCreateWithoutUserInput[] | CrashBetUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CrashBetCreateOrConnectWithoutUserInput | CrashBetCreateOrConnectWithoutUserInput[]
    createMany?: CrashBetCreateManyUserInputEnvelope
    connect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
  }

  export type DoubleBetUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<DoubleBetCreateWithoutUserInput, DoubleBetUncheckedCreateWithoutUserInput> | DoubleBetCreateWithoutUserInput[] | DoubleBetUncheckedCreateWithoutUserInput[]
    connectOrCreate?: DoubleBetCreateOrConnectWithoutUserInput | DoubleBetCreateOrConnectWithoutUserInput[]
    createMany?: DoubleBetCreateManyUserInputEnvelope
    connect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type DecimalFieldUpdateOperationsInput = {
    set?: Decimal | DecimalJsLike | number | string
    increment?: Decimal | DecimalJsLike | number | string
    decrement?: Decimal | DecimalJsLike | number | string
    multiply?: Decimal | DecimalJsLike | number | string
    divide?: Decimal | DecimalJsLike | number | string
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type TransactionUpdateManyWithoutUserNestedInput = {
    create?: XOR<TransactionCreateWithoutUserInput, TransactionUncheckedCreateWithoutUserInput> | TransactionCreateWithoutUserInput[] | TransactionUncheckedCreateWithoutUserInput[]
    connectOrCreate?: TransactionCreateOrConnectWithoutUserInput | TransactionCreateOrConnectWithoutUserInput[]
    upsert?: TransactionUpsertWithWhereUniqueWithoutUserInput | TransactionUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: TransactionCreateManyUserInputEnvelope
    set?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
    disconnect?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
    delete?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
    connect?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
    update?: TransactionUpdateWithWhereUniqueWithoutUserInput | TransactionUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: TransactionUpdateManyWithWhereWithoutUserInput | TransactionUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: TransactionScalarWhereInput | TransactionScalarWhereInput[]
  }

  export type CoinflipRoomUpdateManyWithoutPlayerANestedInput = {
    create?: XOR<CoinflipRoomCreateWithoutPlayerAInput, CoinflipRoomUncheckedCreateWithoutPlayerAInput> | CoinflipRoomCreateWithoutPlayerAInput[] | CoinflipRoomUncheckedCreateWithoutPlayerAInput[]
    connectOrCreate?: CoinflipRoomCreateOrConnectWithoutPlayerAInput | CoinflipRoomCreateOrConnectWithoutPlayerAInput[]
    upsert?: CoinflipRoomUpsertWithWhereUniqueWithoutPlayerAInput | CoinflipRoomUpsertWithWhereUniqueWithoutPlayerAInput[]
    createMany?: CoinflipRoomCreateManyPlayerAInputEnvelope
    set?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    disconnect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    delete?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    connect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    update?: CoinflipRoomUpdateWithWhereUniqueWithoutPlayerAInput | CoinflipRoomUpdateWithWhereUniqueWithoutPlayerAInput[]
    updateMany?: CoinflipRoomUpdateManyWithWhereWithoutPlayerAInput | CoinflipRoomUpdateManyWithWhereWithoutPlayerAInput[]
    deleteMany?: CoinflipRoomScalarWhereInput | CoinflipRoomScalarWhereInput[]
  }

  export type CoinflipRoomUpdateManyWithoutPlayerBNestedInput = {
    create?: XOR<CoinflipRoomCreateWithoutPlayerBInput, CoinflipRoomUncheckedCreateWithoutPlayerBInput> | CoinflipRoomCreateWithoutPlayerBInput[] | CoinflipRoomUncheckedCreateWithoutPlayerBInput[]
    connectOrCreate?: CoinflipRoomCreateOrConnectWithoutPlayerBInput | CoinflipRoomCreateOrConnectWithoutPlayerBInput[]
    upsert?: CoinflipRoomUpsertWithWhereUniqueWithoutPlayerBInput | CoinflipRoomUpsertWithWhereUniqueWithoutPlayerBInput[]
    createMany?: CoinflipRoomCreateManyPlayerBInputEnvelope
    set?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    disconnect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    delete?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    connect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    update?: CoinflipRoomUpdateWithWhereUniqueWithoutPlayerBInput | CoinflipRoomUpdateWithWhereUniqueWithoutPlayerBInput[]
    updateMany?: CoinflipRoomUpdateManyWithWhereWithoutPlayerBInput | CoinflipRoomUpdateManyWithWhereWithoutPlayerBInput[]
    deleteMany?: CoinflipRoomScalarWhereInput | CoinflipRoomScalarWhereInput[]
  }

  export type CrashBetUpdateManyWithoutUserNestedInput = {
    create?: XOR<CrashBetCreateWithoutUserInput, CrashBetUncheckedCreateWithoutUserInput> | CrashBetCreateWithoutUserInput[] | CrashBetUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CrashBetCreateOrConnectWithoutUserInput | CrashBetCreateOrConnectWithoutUserInput[]
    upsert?: CrashBetUpsertWithWhereUniqueWithoutUserInput | CrashBetUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: CrashBetCreateManyUserInputEnvelope
    set?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    disconnect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    delete?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    connect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    update?: CrashBetUpdateWithWhereUniqueWithoutUserInput | CrashBetUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: CrashBetUpdateManyWithWhereWithoutUserInput | CrashBetUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: CrashBetScalarWhereInput | CrashBetScalarWhereInput[]
  }

  export type DoubleBetUpdateManyWithoutUserNestedInput = {
    create?: XOR<DoubleBetCreateWithoutUserInput, DoubleBetUncheckedCreateWithoutUserInput> | DoubleBetCreateWithoutUserInput[] | DoubleBetUncheckedCreateWithoutUserInput[]
    connectOrCreate?: DoubleBetCreateOrConnectWithoutUserInput | DoubleBetCreateOrConnectWithoutUserInput[]
    upsert?: DoubleBetUpsertWithWhereUniqueWithoutUserInput | DoubleBetUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: DoubleBetCreateManyUserInputEnvelope
    set?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    disconnect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    delete?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    connect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    update?: DoubleBetUpdateWithWhereUniqueWithoutUserInput | DoubleBetUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: DoubleBetUpdateManyWithWhereWithoutUserInput | DoubleBetUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: DoubleBetScalarWhereInput | DoubleBetScalarWhereInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type TransactionUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<TransactionCreateWithoutUserInput, TransactionUncheckedCreateWithoutUserInput> | TransactionCreateWithoutUserInput[] | TransactionUncheckedCreateWithoutUserInput[]
    connectOrCreate?: TransactionCreateOrConnectWithoutUserInput | TransactionCreateOrConnectWithoutUserInput[]
    upsert?: TransactionUpsertWithWhereUniqueWithoutUserInput | TransactionUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: TransactionCreateManyUserInputEnvelope
    set?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
    disconnect?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
    delete?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
    connect?: TransactionWhereUniqueInput | TransactionWhereUniqueInput[]
    update?: TransactionUpdateWithWhereUniqueWithoutUserInput | TransactionUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: TransactionUpdateManyWithWhereWithoutUserInput | TransactionUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: TransactionScalarWhereInput | TransactionScalarWhereInput[]
  }

  export type CoinflipRoomUncheckedUpdateManyWithoutPlayerANestedInput = {
    create?: XOR<CoinflipRoomCreateWithoutPlayerAInput, CoinflipRoomUncheckedCreateWithoutPlayerAInput> | CoinflipRoomCreateWithoutPlayerAInput[] | CoinflipRoomUncheckedCreateWithoutPlayerAInput[]
    connectOrCreate?: CoinflipRoomCreateOrConnectWithoutPlayerAInput | CoinflipRoomCreateOrConnectWithoutPlayerAInput[]
    upsert?: CoinflipRoomUpsertWithWhereUniqueWithoutPlayerAInput | CoinflipRoomUpsertWithWhereUniqueWithoutPlayerAInput[]
    createMany?: CoinflipRoomCreateManyPlayerAInputEnvelope
    set?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    disconnect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    delete?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    connect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    update?: CoinflipRoomUpdateWithWhereUniqueWithoutPlayerAInput | CoinflipRoomUpdateWithWhereUniqueWithoutPlayerAInput[]
    updateMany?: CoinflipRoomUpdateManyWithWhereWithoutPlayerAInput | CoinflipRoomUpdateManyWithWhereWithoutPlayerAInput[]
    deleteMany?: CoinflipRoomScalarWhereInput | CoinflipRoomScalarWhereInput[]
  }

  export type CoinflipRoomUncheckedUpdateManyWithoutPlayerBNestedInput = {
    create?: XOR<CoinflipRoomCreateWithoutPlayerBInput, CoinflipRoomUncheckedCreateWithoutPlayerBInput> | CoinflipRoomCreateWithoutPlayerBInput[] | CoinflipRoomUncheckedCreateWithoutPlayerBInput[]
    connectOrCreate?: CoinflipRoomCreateOrConnectWithoutPlayerBInput | CoinflipRoomCreateOrConnectWithoutPlayerBInput[]
    upsert?: CoinflipRoomUpsertWithWhereUniqueWithoutPlayerBInput | CoinflipRoomUpsertWithWhereUniqueWithoutPlayerBInput[]
    createMany?: CoinflipRoomCreateManyPlayerBInputEnvelope
    set?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    disconnect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    delete?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    connect?: CoinflipRoomWhereUniqueInput | CoinflipRoomWhereUniqueInput[]
    update?: CoinflipRoomUpdateWithWhereUniqueWithoutPlayerBInput | CoinflipRoomUpdateWithWhereUniqueWithoutPlayerBInput[]
    updateMany?: CoinflipRoomUpdateManyWithWhereWithoutPlayerBInput | CoinflipRoomUpdateManyWithWhereWithoutPlayerBInput[]
    deleteMany?: CoinflipRoomScalarWhereInput | CoinflipRoomScalarWhereInput[]
  }

  export type CrashBetUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<CrashBetCreateWithoutUserInput, CrashBetUncheckedCreateWithoutUserInput> | CrashBetCreateWithoutUserInput[] | CrashBetUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CrashBetCreateOrConnectWithoutUserInput | CrashBetCreateOrConnectWithoutUserInput[]
    upsert?: CrashBetUpsertWithWhereUniqueWithoutUserInput | CrashBetUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: CrashBetCreateManyUserInputEnvelope
    set?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    disconnect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    delete?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    connect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    update?: CrashBetUpdateWithWhereUniqueWithoutUserInput | CrashBetUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: CrashBetUpdateManyWithWhereWithoutUserInput | CrashBetUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: CrashBetScalarWhereInput | CrashBetScalarWhereInput[]
  }

  export type DoubleBetUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<DoubleBetCreateWithoutUserInput, DoubleBetUncheckedCreateWithoutUserInput> | DoubleBetCreateWithoutUserInput[] | DoubleBetUncheckedCreateWithoutUserInput[]
    connectOrCreate?: DoubleBetCreateOrConnectWithoutUserInput | DoubleBetCreateOrConnectWithoutUserInput[]
    upsert?: DoubleBetUpsertWithWhereUniqueWithoutUserInput | DoubleBetUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: DoubleBetCreateManyUserInputEnvelope
    set?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    disconnect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    delete?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    connect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    update?: DoubleBetUpdateWithWhereUniqueWithoutUserInput | DoubleBetUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: DoubleBetUpdateManyWithWhereWithoutUserInput | DoubleBetUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: DoubleBetScalarWhereInput | DoubleBetScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutTransactionsInput = {
    create?: XOR<UserCreateWithoutTransactionsInput, UserUncheckedCreateWithoutTransactionsInput>
    connectOrCreate?: UserCreateOrConnectWithoutTransactionsInput
    connect?: UserWhereUniqueInput
  }

  export type UserUpdateOneWithoutTransactionsNestedInput = {
    create?: XOR<UserCreateWithoutTransactionsInput, UserUncheckedCreateWithoutTransactionsInput>
    connectOrCreate?: UserCreateOrConnectWithoutTransactionsInput
    upsert?: UserUpsertWithoutTransactionsInput
    disconnect?: UserWhereInput | boolean
    delete?: UserWhereInput | boolean
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutTransactionsInput, UserUpdateWithoutTransactionsInput>, UserUncheckedUpdateWithoutTransactionsInput>
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type UserCreateNestedOneWithoutCoinflipsAInput = {
    create?: XOR<UserCreateWithoutCoinflipsAInput, UserUncheckedCreateWithoutCoinflipsAInput>
    connectOrCreate?: UserCreateOrConnectWithoutCoinflipsAInput
    connect?: UserWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutCoinflipsBInput = {
    create?: XOR<UserCreateWithoutCoinflipsBInput, UserUncheckedCreateWithoutCoinflipsBInput>
    connectOrCreate?: UserCreateOrConnectWithoutCoinflipsBInput
    connect?: UserWhereUniqueInput
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type UserUpdateOneRequiredWithoutCoinflipsANestedInput = {
    create?: XOR<UserCreateWithoutCoinflipsAInput, UserUncheckedCreateWithoutCoinflipsAInput>
    connectOrCreate?: UserCreateOrConnectWithoutCoinflipsAInput
    upsert?: UserUpsertWithoutCoinflipsAInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutCoinflipsAInput, UserUpdateWithoutCoinflipsAInput>, UserUncheckedUpdateWithoutCoinflipsAInput>
  }

  export type UserUpdateOneWithoutCoinflipsBNestedInput = {
    create?: XOR<UserCreateWithoutCoinflipsBInput, UserUncheckedCreateWithoutCoinflipsBInput>
    connectOrCreate?: UserCreateOrConnectWithoutCoinflipsBInput
    upsert?: UserUpsertWithoutCoinflipsBInput
    disconnect?: UserWhereInput | boolean
    delete?: UserWhereInput | boolean
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutCoinflipsBInput, UserUpdateWithoutCoinflipsBInput>, UserUncheckedUpdateWithoutCoinflipsBInput>
  }

  export type CrashBetCreateNestedManyWithoutRoundInput = {
    create?: XOR<CrashBetCreateWithoutRoundInput, CrashBetUncheckedCreateWithoutRoundInput> | CrashBetCreateWithoutRoundInput[] | CrashBetUncheckedCreateWithoutRoundInput[]
    connectOrCreate?: CrashBetCreateOrConnectWithoutRoundInput | CrashBetCreateOrConnectWithoutRoundInput[]
    createMany?: CrashBetCreateManyRoundInputEnvelope
    connect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
  }

  export type CrashBetUncheckedCreateNestedManyWithoutRoundInput = {
    create?: XOR<CrashBetCreateWithoutRoundInput, CrashBetUncheckedCreateWithoutRoundInput> | CrashBetCreateWithoutRoundInput[] | CrashBetUncheckedCreateWithoutRoundInput[]
    connectOrCreate?: CrashBetCreateOrConnectWithoutRoundInput | CrashBetCreateOrConnectWithoutRoundInput[]
    createMany?: CrashBetCreateManyRoundInputEnvelope
    connect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
  }

  export type CrashBetUpdateManyWithoutRoundNestedInput = {
    create?: XOR<CrashBetCreateWithoutRoundInput, CrashBetUncheckedCreateWithoutRoundInput> | CrashBetCreateWithoutRoundInput[] | CrashBetUncheckedCreateWithoutRoundInput[]
    connectOrCreate?: CrashBetCreateOrConnectWithoutRoundInput | CrashBetCreateOrConnectWithoutRoundInput[]
    upsert?: CrashBetUpsertWithWhereUniqueWithoutRoundInput | CrashBetUpsertWithWhereUniqueWithoutRoundInput[]
    createMany?: CrashBetCreateManyRoundInputEnvelope
    set?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    disconnect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    delete?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    connect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    update?: CrashBetUpdateWithWhereUniqueWithoutRoundInput | CrashBetUpdateWithWhereUniqueWithoutRoundInput[]
    updateMany?: CrashBetUpdateManyWithWhereWithoutRoundInput | CrashBetUpdateManyWithWhereWithoutRoundInput[]
    deleteMany?: CrashBetScalarWhereInput | CrashBetScalarWhereInput[]
  }

  export type CrashBetUncheckedUpdateManyWithoutRoundNestedInput = {
    create?: XOR<CrashBetCreateWithoutRoundInput, CrashBetUncheckedCreateWithoutRoundInput> | CrashBetCreateWithoutRoundInput[] | CrashBetUncheckedCreateWithoutRoundInput[]
    connectOrCreate?: CrashBetCreateOrConnectWithoutRoundInput | CrashBetCreateOrConnectWithoutRoundInput[]
    upsert?: CrashBetUpsertWithWhereUniqueWithoutRoundInput | CrashBetUpsertWithWhereUniqueWithoutRoundInput[]
    createMany?: CrashBetCreateManyRoundInputEnvelope
    set?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    disconnect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    delete?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    connect?: CrashBetWhereUniqueInput | CrashBetWhereUniqueInput[]
    update?: CrashBetUpdateWithWhereUniqueWithoutRoundInput | CrashBetUpdateWithWhereUniqueWithoutRoundInput[]
    updateMany?: CrashBetUpdateManyWithWhereWithoutRoundInput | CrashBetUpdateManyWithWhereWithoutRoundInput[]
    deleteMany?: CrashBetScalarWhereInput | CrashBetScalarWhereInput[]
  }

  export type CrashRoundCreateNestedOneWithoutBetsInput = {
    create?: XOR<CrashRoundCreateWithoutBetsInput, CrashRoundUncheckedCreateWithoutBetsInput>
    connectOrCreate?: CrashRoundCreateOrConnectWithoutBetsInput
    connect?: CrashRoundWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutCrashBetsInput = {
    create?: XOR<UserCreateWithoutCrashBetsInput, UserUncheckedCreateWithoutCrashBetsInput>
    connectOrCreate?: UserCreateOrConnectWithoutCrashBetsInput
    connect?: UserWhereUniqueInput
  }

  export type NullableDecimalFieldUpdateOperationsInput = {
    set?: Decimal | DecimalJsLike | number | string | null
    increment?: Decimal | DecimalJsLike | number | string
    decrement?: Decimal | DecimalJsLike | number | string
    multiply?: Decimal | DecimalJsLike | number | string
    divide?: Decimal | DecimalJsLike | number | string
  }

  export type CrashRoundUpdateOneRequiredWithoutBetsNestedInput = {
    create?: XOR<CrashRoundCreateWithoutBetsInput, CrashRoundUncheckedCreateWithoutBetsInput>
    connectOrCreate?: CrashRoundCreateOrConnectWithoutBetsInput
    upsert?: CrashRoundUpsertWithoutBetsInput
    connect?: CrashRoundWhereUniqueInput
    update?: XOR<XOR<CrashRoundUpdateToOneWithWhereWithoutBetsInput, CrashRoundUpdateWithoutBetsInput>, CrashRoundUncheckedUpdateWithoutBetsInput>
  }

  export type UserUpdateOneRequiredWithoutCrashBetsNestedInput = {
    create?: XOR<UserCreateWithoutCrashBetsInput, UserUncheckedCreateWithoutCrashBetsInput>
    connectOrCreate?: UserCreateOrConnectWithoutCrashBetsInput
    upsert?: UserUpsertWithoutCrashBetsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutCrashBetsInput, UserUpdateWithoutCrashBetsInput>, UserUncheckedUpdateWithoutCrashBetsInput>
  }

  export type DoubleBetCreateNestedManyWithoutRoundInput = {
    create?: XOR<DoubleBetCreateWithoutRoundInput, DoubleBetUncheckedCreateWithoutRoundInput> | DoubleBetCreateWithoutRoundInput[] | DoubleBetUncheckedCreateWithoutRoundInput[]
    connectOrCreate?: DoubleBetCreateOrConnectWithoutRoundInput | DoubleBetCreateOrConnectWithoutRoundInput[]
    createMany?: DoubleBetCreateManyRoundInputEnvelope
    connect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
  }

  export type DoubleBetUncheckedCreateNestedManyWithoutRoundInput = {
    create?: XOR<DoubleBetCreateWithoutRoundInput, DoubleBetUncheckedCreateWithoutRoundInput> | DoubleBetCreateWithoutRoundInput[] | DoubleBetUncheckedCreateWithoutRoundInput[]
    connectOrCreate?: DoubleBetCreateOrConnectWithoutRoundInput | DoubleBetCreateOrConnectWithoutRoundInput[]
    createMany?: DoubleBetCreateManyRoundInputEnvelope
    connect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
  }

  export type DoubleBetUpdateManyWithoutRoundNestedInput = {
    create?: XOR<DoubleBetCreateWithoutRoundInput, DoubleBetUncheckedCreateWithoutRoundInput> | DoubleBetCreateWithoutRoundInput[] | DoubleBetUncheckedCreateWithoutRoundInput[]
    connectOrCreate?: DoubleBetCreateOrConnectWithoutRoundInput | DoubleBetCreateOrConnectWithoutRoundInput[]
    upsert?: DoubleBetUpsertWithWhereUniqueWithoutRoundInput | DoubleBetUpsertWithWhereUniqueWithoutRoundInput[]
    createMany?: DoubleBetCreateManyRoundInputEnvelope
    set?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    disconnect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    delete?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    connect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    update?: DoubleBetUpdateWithWhereUniqueWithoutRoundInput | DoubleBetUpdateWithWhereUniqueWithoutRoundInput[]
    updateMany?: DoubleBetUpdateManyWithWhereWithoutRoundInput | DoubleBetUpdateManyWithWhereWithoutRoundInput[]
    deleteMany?: DoubleBetScalarWhereInput | DoubleBetScalarWhereInput[]
  }

  export type DoubleBetUncheckedUpdateManyWithoutRoundNestedInput = {
    create?: XOR<DoubleBetCreateWithoutRoundInput, DoubleBetUncheckedCreateWithoutRoundInput> | DoubleBetCreateWithoutRoundInput[] | DoubleBetUncheckedCreateWithoutRoundInput[]
    connectOrCreate?: DoubleBetCreateOrConnectWithoutRoundInput | DoubleBetCreateOrConnectWithoutRoundInput[]
    upsert?: DoubleBetUpsertWithWhereUniqueWithoutRoundInput | DoubleBetUpsertWithWhereUniqueWithoutRoundInput[]
    createMany?: DoubleBetCreateManyRoundInputEnvelope
    set?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    disconnect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    delete?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    connect?: DoubleBetWhereUniqueInput | DoubleBetWhereUniqueInput[]
    update?: DoubleBetUpdateWithWhereUniqueWithoutRoundInput | DoubleBetUpdateWithWhereUniqueWithoutRoundInput[]
    updateMany?: DoubleBetUpdateManyWithWhereWithoutRoundInput | DoubleBetUpdateManyWithWhereWithoutRoundInput[]
    deleteMany?: DoubleBetScalarWhereInput | DoubleBetScalarWhereInput[]
  }

  export type DoubleRoundCreateNestedOneWithoutBetsInput = {
    create?: XOR<DoubleRoundCreateWithoutBetsInput, DoubleRoundUncheckedCreateWithoutBetsInput>
    connectOrCreate?: DoubleRoundCreateOrConnectWithoutBetsInput
    connect?: DoubleRoundWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutDoubleBetsInput = {
    create?: XOR<UserCreateWithoutDoubleBetsInput, UserUncheckedCreateWithoutDoubleBetsInput>
    connectOrCreate?: UserCreateOrConnectWithoutDoubleBetsInput
    connect?: UserWhereUniqueInput
  }

  export type DoubleRoundUpdateOneRequiredWithoutBetsNestedInput = {
    create?: XOR<DoubleRoundCreateWithoutBetsInput, DoubleRoundUncheckedCreateWithoutBetsInput>
    connectOrCreate?: DoubleRoundCreateOrConnectWithoutBetsInput
    upsert?: DoubleRoundUpsertWithoutBetsInput
    connect?: DoubleRoundWhereUniqueInput
    update?: XOR<XOR<DoubleRoundUpdateToOneWithWhereWithoutBetsInput, DoubleRoundUpdateWithoutBetsInput>, DoubleRoundUncheckedUpdateWithoutBetsInput>
  }

  export type UserUpdateOneRequiredWithoutDoubleBetsNestedInput = {
    create?: XOR<UserCreateWithoutDoubleBetsInput, UserUncheckedCreateWithoutDoubleBetsInput>
    connectOrCreate?: UserCreateOrConnectWithoutDoubleBetsInput
    upsert?: UserUpsertWithoutDoubleBetsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutDoubleBetsInput, UserUpdateWithoutDoubleBetsInput>, UserUncheckedUpdateWithoutDoubleBetsInput>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDecimalFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    in?: Decimal[] | DecimalJsLike[] | number[] | string[]
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[]
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDecimalWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    in?: Decimal[] | DecimalJsLike[] | number[] | string[]
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[]
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalWithAggregatesFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedDecimalFilter<$PrismaModel>
    _sum?: NestedDecimalFilter<$PrismaModel>
    _min?: NestedDecimalFilter<$PrismaModel>
    _max?: NestedDecimalFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedDecimalNullableFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel> | null
    in?: Decimal[] | DecimalJsLike[] | number[] | string[] | null
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[] | null
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalNullableFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string | null
  }

  export type NestedDecimalNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel> | null
    in?: Decimal[] | DecimalJsLike[] | number[] | string[] | null
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[] | null
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalNullableWithAggregatesFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedDecimalNullableFilter<$PrismaModel>
    _sum?: NestedDecimalNullableFilter<$PrismaModel>
    _min?: NestedDecimalNullableFilter<$PrismaModel>
    _max?: NestedDecimalNullableFilter<$PrismaModel>
  }

  export type TransactionCreateWithoutUserInput = {
    kind: string
    amountTon: Decimal | DecimalJsLike | number | string
    ref?: string | null
    createdAt?: Date | string
  }

  export type TransactionUncheckedCreateWithoutUserInput = {
    id?: number
    kind: string
    amountTon: Decimal | DecimalJsLike | number | string
    ref?: string | null
    createdAt?: Date | string
  }

  export type TransactionCreateOrConnectWithoutUserInput = {
    where: TransactionWhereUniqueInput
    create: XOR<TransactionCreateWithoutUserInput, TransactionUncheckedCreateWithoutUserInput>
  }

  export type TransactionCreateManyUserInputEnvelope = {
    data: TransactionCreateManyUserInput | TransactionCreateManyUserInput[]
  }

  export type CoinflipRoomCreateWithoutPlayerAInput = {
    stakeTon: Decimal | DecimalJsLike | number | string
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
    playerB?: UserCreateNestedOneWithoutCoinflipsBInput
  }

  export type CoinflipRoomUncheckedCreateWithoutPlayerAInput = {
    id?: number
    stakeTon: Decimal | DecimalJsLike | number | string
    playerBId?: number | null
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
  }

  export type CoinflipRoomCreateOrConnectWithoutPlayerAInput = {
    where: CoinflipRoomWhereUniqueInput
    create: XOR<CoinflipRoomCreateWithoutPlayerAInput, CoinflipRoomUncheckedCreateWithoutPlayerAInput>
  }

  export type CoinflipRoomCreateManyPlayerAInputEnvelope = {
    data: CoinflipRoomCreateManyPlayerAInput | CoinflipRoomCreateManyPlayerAInput[]
  }

  export type CoinflipRoomCreateWithoutPlayerBInput = {
    stakeTon: Decimal | DecimalJsLike | number | string
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
    playerA: UserCreateNestedOneWithoutCoinflipsAInput
  }

  export type CoinflipRoomUncheckedCreateWithoutPlayerBInput = {
    id?: number
    stakeTon: Decimal | DecimalJsLike | number | string
    playerAId: number
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
  }

  export type CoinflipRoomCreateOrConnectWithoutPlayerBInput = {
    where: CoinflipRoomWhereUniqueInput
    create: XOR<CoinflipRoomCreateWithoutPlayerBInput, CoinflipRoomUncheckedCreateWithoutPlayerBInput>
  }

  export type CoinflipRoomCreateManyPlayerBInputEnvelope = {
    data: CoinflipRoomCreateManyPlayerBInput | CoinflipRoomCreateManyPlayerBInput[]
  }

  export type CrashBetCreateWithoutUserInput = {
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
    round: CrashRoundCreateNestedOneWithoutBetsInput
  }

  export type CrashBetUncheckedCreateWithoutUserInput = {
    id?: number
    roundId: number
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type CrashBetCreateOrConnectWithoutUserInput = {
    where: CrashBetWhereUniqueInput
    create: XOR<CrashBetCreateWithoutUserInput, CrashBetUncheckedCreateWithoutUserInput>
  }

  export type CrashBetCreateManyUserInputEnvelope = {
    data: CrashBetCreateManyUserInput | CrashBetCreateManyUserInput[]
  }

  export type DoubleBetCreateWithoutUserInput = {
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
    round: DoubleRoundCreateNestedOneWithoutBetsInput
  }

  export type DoubleBetUncheckedCreateWithoutUserInput = {
    id?: number
    roundId: number
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type DoubleBetCreateOrConnectWithoutUserInput = {
    where: DoubleBetWhereUniqueInput
    create: XOR<DoubleBetCreateWithoutUserInput, DoubleBetUncheckedCreateWithoutUserInput>
  }

  export type DoubleBetCreateManyUserInputEnvelope = {
    data: DoubleBetCreateManyUserInput | DoubleBetCreateManyUserInput[]
  }

  export type TransactionUpsertWithWhereUniqueWithoutUserInput = {
    where: TransactionWhereUniqueInput
    update: XOR<TransactionUpdateWithoutUserInput, TransactionUncheckedUpdateWithoutUserInput>
    create: XOR<TransactionCreateWithoutUserInput, TransactionUncheckedCreateWithoutUserInput>
  }

  export type TransactionUpdateWithWhereUniqueWithoutUserInput = {
    where: TransactionWhereUniqueInput
    data: XOR<TransactionUpdateWithoutUserInput, TransactionUncheckedUpdateWithoutUserInput>
  }

  export type TransactionUpdateManyWithWhereWithoutUserInput = {
    where: TransactionScalarWhereInput
    data: XOR<TransactionUpdateManyMutationInput, TransactionUncheckedUpdateManyWithoutUserInput>
  }

  export type TransactionScalarWhereInput = {
    AND?: TransactionScalarWhereInput | TransactionScalarWhereInput[]
    OR?: TransactionScalarWhereInput[]
    NOT?: TransactionScalarWhereInput | TransactionScalarWhereInput[]
    id?: IntFilter<"Transaction"> | number
    kind?: StringFilter<"Transaction"> | string
    amountTon?: DecimalFilter<"Transaction"> | Decimal | DecimalJsLike | number | string
    ref?: StringNullableFilter<"Transaction"> | string | null
    userId?: IntNullableFilter<"Transaction"> | number | null
    createdAt?: DateTimeFilter<"Transaction"> | Date | string
  }

  export type CoinflipRoomUpsertWithWhereUniqueWithoutPlayerAInput = {
    where: CoinflipRoomWhereUniqueInput
    update: XOR<CoinflipRoomUpdateWithoutPlayerAInput, CoinflipRoomUncheckedUpdateWithoutPlayerAInput>
    create: XOR<CoinflipRoomCreateWithoutPlayerAInput, CoinflipRoomUncheckedCreateWithoutPlayerAInput>
  }

  export type CoinflipRoomUpdateWithWhereUniqueWithoutPlayerAInput = {
    where: CoinflipRoomWhereUniqueInput
    data: XOR<CoinflipRoomUpdateWithoutPlayerAInput, CoinflipRoomUncheckedUpdateWithoutPlayerAInput>
  }

  export type CoinflipRoomUpdateManyWithWhereWithoutPlayerAInput = {
    where: CoinflipRoomScalarWhereInput
    data: XOR<CoinflipRoomUpdateManyMutationInput, CoinflipRoomUncheckedUpdateManyWithoutPlayerAInput>
  }

  export type CoinflipRoomScalarWhereInput = {
    AND?: CoinflipRoomScalarWhereInput | CoinflipRoomScalarWhereInput[]
    OR?: CoinflipRoomScalarWhereInput[]
    NOT?: CoinflipRoomScalarWhereInput | CoinflipRoomScalarWhereInput[]
    id?: IntFilter<"CoinflipRoom"> | number
    stakeTon?: DecimalFilter<"CoinflipRoom"> | Decimal | DecimalJsLike | number | string
    playerAId?: IntFilter<"CoinflipRoom"> | number
    playerBId?: IntNullableFilter<"CoinflipRoom"> | number | null
    seedA?: StringNullableFilter<"CoinflipRoom"> | string | null
    seedB?: StringNullableFilter<"CoinflipRoom"> | string | null
    winnerId?: IntNullableFilter<"CoinflipRoom"> | number | null
    status?: StringFilter<"CoinflipRoom"> | string
    createdAt?: DateTimeFilter<"CoinflipRoom"> | Date | string
    finishedAt?: DateTimeNullableFilter<"CoinflipRoom"> | Date | string | null
  }

  export type CoinflipRoomUpsertWithWhereUniqueWithoutPlayerBInput = {
    where: CoinflipRoomWhereUniqueInput
    update: XOR<CoinflipRoomUpdateWithoutPlayerBInput, CoinflipRoomUncheckedUpdateWithoutPlayerBInput>
    create: XOR<CoinflipRoomCreateWithoutPlayerBInput, CoinflipRoomUncheckedCreateWithoutPlayerBInput>
  }

  export type CoinflipRoomUpdateWithWhereUniqueWithoutPlayerBInput = {
    where: CoinflipRoomWhereUniqueInput
    data: XOR<CoinflipRoomUpdateWithoutPlayerBInput, CoinflipRoomUncheckedUpdateWithoutPlayerBInput>
  }

  export type CoinflipRoomUpdateManyWithWhereWithoutPlayerBInput = {
    where: CoinflipRoomScalarWhereInput
    data: XOR<CoinflipRoomUpdateManyMutationInput, CoinflipRoomUncheckedUpdateManyWithoutPlayerBInput>
  }

  export type CrashBetUpsertWithWhereUniqueWithoutUserInput = {
    where: CrashBetWhereUniqueInput
    update: XOR<CrashBetUpdateWithoutUserInput, CrashBetUncheckedUpdateWithoutUserInput>
    create: XOR<CrashBetCreateWithoutUserInput, CrashBetUncheckedCreateWithoutUserInput>
  }

  export type CrashBetUpdateWithWhereUniqueWithoutUserInput = {
    where: CrashBetWhereUniqueInput
    data: XOR<CrashBetUpdateWithoutUserInput, CrashBetUncheckedUpdateWithoutUserInput>
  }

  export type CrashBetUpdateManyWithWhereWithoutUserInput = {
    where: CrashBetScalarWhereInput
    data: XOR<CrashBetUpdateManyMutationInput, CrashBetUncheckedUpdateManyWithoutUserInput>
  }

  export type CrashBetScalarWhereInput = {
    AND?: CrashBetScalarWhereInput | CrashBetScalarWhereInput[]
    OR?: CrashBetScalarWhereInput[]
    NOT?: CrashBetScalarWhereInput | CrashBetScalarWhereInput[]
    id?: IntFilter<"CrashBet"> | number
    roundId?: IntFilter<"CrashBet"> | number
    userId?: IntFilter<"CrashBet"> | number
    betTon?: DecimalFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string
    cashoutAt?: DecimalNullableFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string | null
    winTon?: DecimalNullableFilter<"CrashBet"> | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFilter<"CrashBet"> | Date | string
  }

  export type DoubleBetUpsertWithWhereUniqueWithoutUserInput = {
    where: DoubleBetWhereUniqueInput
    update: XOR<DoubleBetUpdateWithoutUserInput, DoubleBetUncheckedUpdateWithoutUserInput>
    create: XOR<DoubleBetCreateWithoutUserInput, DoubleBetUncheckedCreateWithoutUserInput>
  }

  export type DoubleBetUpdateWithWhereUniqueWithoutUserInput = {
    where: DoubleBetWhereUniqueInput
    data: XOR<DoubleBetUpdateWithoutUserInput, DoubleBetUncheckedUpdateWithoutUserInput>
  }

  export type DoubleBetUpdateManyWithWhereWithoutUserInput = {
    where: DoubleBetScalarWhereInput
    data: XOR<DoubleBetUpdateManyMutationInput, DoubleBetUncheckedUpdateManyWithoutUserInput>
  }

  export type DoubleBetScalarWhereInput = {
    AND?: DoubleBetScalarWhereInput | DoubleBetScalarWhereInput[]
    OR?: DoubleBetScalarWhereInput[]
    NOT?: DoubleBetScalarWhereInput | DoubleBetScalarWhereInput[]
    id?: IntFilter<"DoubleBet"> | number
    roundId?: IntFilter<"DoubleBet"> | number
    userId?: IntFilter<"DoubleBet"> | number
    betTon?: DecimalFilter<"DoubleBet"> | Decimal | DecimalJsLike | number | string
    color?: StringFilter<"DoubleBet"> | string
    winTon?: DecimalNullableFilter<"DoubleBet"> | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFilter<"DoubleBet"> | Date | string
  }

  export type UserCreateWithoutTransactionsInput = {
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    coinflipsA?: CoinflipRoomCreateNestedManyWithoutPlayerAInput
    coinflipsB?: CoinflipRoomCreateNestedManyWithoutPlayerBInput
    crashBets?: CrashBetCreateNestedManyWithoutUserInput
    doubleBets?: DoubleBetCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutTransactionsInput = {
    id?: number
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    coinflipsA?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerAInput
    coinflipsB?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerBInput
    crashBets?: CrashBetUncheckedCreateNestedManyWithoutUserInput
    doubleBets?: DoubleBetUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutTransactionsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutTransactionsInput, UserUncheckedCreateWithoutTransactionsInput>
  }

  export type UserUpsertWithoutTransactionsInput = {
    update: XOR<UserUpdateWithoutTransactionsInput, UserUncheckedUpdateWithoutTransactionsInput>
    create: XOR<UserCreateWithoutTransactionsInput, UserUncheckedCreateWithoutTransactionsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutTransactionsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutTransactionsInput, UserUncheckedUpdateWithoutTransactionsInput>
  }

  export type UserUpdateWithoutTransactionsInput = {
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    coinflipsA?: CoinflipRoomUpdateManyWithoutPlayerANestedInput
    coinflipsB?: CoinflipRoomUpdateManyWithoutPlayerBNestedInput
    crashBets?: CrashBetUpdateManyWithoutUserNestedInput
    doubleBets?: DoubleBetUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutTransactionsInput = {
    id?: IntFieldUpdateOperationsInput | number
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    coinflipsA?: CoinflipRoomUncheckedUpdateManyWithoutPlayerANestedInput
    coinflipsB?: CoinflipRoomUncheckedUpdateManyWithoutPlayerBNestedInput
    crashBets?: CrashBetUncheckedUpdateManyWithoutUserNestedInput
    doubleBets?: DoubleBetUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateWithoutCoinflipsAInput = {
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionCreateNestedManyWithoutUserInput
    coinflipsB?: CoinflipRoomCreateNestedManyWithoutPlayerBInput
    crashBets?: CrashBetCreateNestedManyWithoutUserInput
    doubleBets?: DoubleBetCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutCoinflipsAInput = {
    id?: number
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionUncheckedCreateNestedManyWithoutUserInput
    coinflipsB?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerBInput
    crashBets?: CrashBetUncheckedCreateNestedManyWithoutUserInput
    doubleBets?: DoubleBetUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutCoinflipsAInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutCoinflipsAInput, UserUncheckedCreateWithoutCoinflipsAInput>
  }

  export type UserCreateWithoutCoinflipsBInput = {
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionCreateNestedManyWithoutUserInput
    coinflipsA?: CoinflipRoomCreateNestedManyWithoutPlayerAInput
    crashBets?: CrashBetCreateNestedManyWithoutUserInput
    doubleBets?: DoubleBetCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutCoinflipsBInput = {
    id?: number
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionUncheckedCreateNestedManyWithoutUserInput
    coinflipsA?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerAInput
    crashBets?: CrashBetUncheckedCreateNestedManyWithoutUserInput
    doubleBets?: DoubleBetUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutCoinflipsBInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutCoinflipsBInput, UserUncheckedCreateWithoutCoinflipsBInput>
  }

  export type UserUpsertWithoutCoinflipsAInput = {
    update: XOR<UserUpdateWithoutCoinflipsAInput, UserUncheckedUpdateWithoutCoinflipsAInput>
    create: XOR<UserCreateWithoutCoinflipsAInput, UserUncheckedCreateWithoutCoinflipsAInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutCoinflipsAInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutCoinflipsAInput, UserUncheckedUpdateWithoutCoinflipsAInput>
  }

  export type UserUpdateWithoutCoinflipsAInput = {
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUpdateManyWithoutUserNestedInput
    coinflipsB?: CoinflipRoomUpdateManyWithoutPlayerBNestedInput
    crashBets?: CrashBetUpdateManyWithoutUserNestedInput
    doubleBets?: DoubleBetUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutCoinflipsAInput = {
    id?: IntFieldUpdateOperationsInput | number
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUncheckedUpdateManyWithoutUserNestedInput
    coinflipsB?: CoinflipRoomUncheckedUpdateManyWithoutPlayerBNestedInput
    crashBets?: CrashBetUncheckedUpdateManyWithoutUserNestedInput
    doubleBets?: DoubleBetUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserUpsertWithoutCoinflipsBInput = {
    update: XOR<UserUpdateWithoutCoinflipsBInput, UserUncheckedUpdateWithoutCoinflipsBInput>
    create: XOR<UserCreateWithoutCoinflipsBInput, UserUncheckedCreateWithoutCoinflipsBInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutCoinflipsBInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutCoinflipsBInput, UserUncheckedUpdateWithoutCoinflipsBInput>
  }

  export type UserUpdateWithoutCoinflipsBInput = {
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUpdateManyWithoutUserNestedInput
    coinflipsA?: CoinflipRoomUpdateManyWithoutPlayerANestedInput
    crashBets?: CrashBetUpdateManyWithoutUserNestedInput
    doubleBets?: DoubleBetUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutCoinflipsBInput = {
    id?: IntFieldUpdateOperationsInput | number
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUncheckedUpdateManyWithoutUserNestedInput
    coinflipsA?: CoinflipRoomUncheckedUpdateManyWithoutPlayerANestedInput
    crashBets?: CrashBetUncheckedUpdateManyWithoutUserNestedInput
    doubleBets?: DoubleBetUncheckedUpdateManyWithoutUserNestedInput
  }

  export type CrashBetCreateWithoutRoundInput = {
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutCrashBetsInput
  }

  export type CrashBetUncheckedCreateWithoutRoundInput = {
    id?: number
    userId: number
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type CrashBetCreateOrConnectWithoutRoundInput = {
    where: CrashBetWhereUniqueInput
    create: XOR<CrashBetCreateWithoutRoundInput, CrashBetUncheckedCreateWithoutRoundInput>
  }

  export type CrashBetCreateManyRoundInputEnvelope = {
    data: CrashBetCreateManyRoundInput | CrashBetCreateManyRoundInput[]
  }

  export type CrashBetUpsertWithWhereUniqueWithoutRoundInput = {
    where: CrashBetWhereUniqueInput
    update: XOR<CrashBetUpdateWithoutRoundInput, CrashBetUncheckedUpdateWithoutRoundInput>
    create: XOR<CrashBetCreateWithoutRoundInput, CrashBetUncheckedCreateWithoutRoundInput>
  }

  export type CrashBetUpdateWithWhereUniqueWithoutRoundInput = {
    where: CrashBetWhereUniqueInput
    data: XOR<CrashBetUpdateWithoutRoundInput, CrashBetUncheckedUpdateWithoutRoundInput>
  }

  export type CrashBetUpdateManyWithWhereWithoutRoundInput = {
    where: CrashBetScalarWhereInput
    data: XOR<CrashBetUpdateManyMutationInput, CrashBetUncheckedUpdateManyWithoutRoundInput>
  }

  export type CrashRoundCreateWithoutBetsInput = {
    multiplier: Decimal | DecimalJsLike | number | string
    seed: string
    status?: string
    createdAt?: Date | string
    crashedAt?: Date | string | null
  }

  export type CrashRoundUncheckedCreateWithoutBetsInput = {
    id?: number
    multiplier: Decimal | DecimalJsLike | number | string
    seed: string
    status?: string
    createdAt?: Date | string
    crashedAt?: Date | string | null
  }

  export type CrashRoundCreateOrConnectWithoutBetsInput = {
    where: CrashRoundWhereUniqueInput
    create: XOR<CrashRoundCreateWithoutBetsInput, CrashRoundUncheckedCreateWithoutBetsInput>
  }

  export type UserCreateWithoutCrashBetsInput = {
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionCreateNestedManyWithoutUserInput
    coinflipsA?: CoinflipRoomCreateNestedManyWithoutPlayerAInput
    coinflipsB?: CoinflipRoomCreateNestedManyWithoutPlayerBInput
    doubleBets?: DoubleBetCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutCrashBetsInput = {
    id?: number
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionUncheckedCreateNestedManyWithoutUserInput
    coinflipsA?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerAInput
    coinflipsB?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerBInput
    doubleBets?: DoubleBetUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutCrashBetsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutCrashBetsInput, UserUncheckedCreateWithoutCrashBetsInput>
  }

  export type CrashRoundUpsertWithoutBetsInput = {
    update: XOR<CrashRoundUpdateWithoutBetsInput, CrashRoundUncheckedUpdateWithoutBetsInput>
    create: XOR<CrashRoundCreateWithoutBetsInput, CrashRoundUncheckedCreateWithoutBetsInput>
    where?: CrashRoundWhereInput
  }

  export type CrashRoundUpdateToOneWithWhereWithoutBetsInput = {
    where?: CrashRoundWhereInput
    data: XOR<CrashRoundUpdateWithoutBetsInput, CrashRoundUncheckedUpdateWithoutBetsInput>
  }

  export type CrashRoundUpdateWithoutBetsInput = {
    multiplier?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seed?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    crashedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrashRoundUncheckedUpdateWithoutBetsInput = {
    id?: IntFieldUpdateOperationsInput | number
    multiplier?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seed?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    crashedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type UserUpsertWithoutCrashBetsInput = {
    update: XOR<UserUpdateWithoutCrashBetsInput, UserUncheckedUpdateWithoutCrashBetsInput>
    create: XOR<UserCreateWithoutCrashBetsInput, UserUncheckedCreateWithoutCrashBetsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutCrashBetsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutCrashBetsInput, UserUncheckedUpdateWithoutCrashBetsInput>
  }

  export type UserUpdateWithoutCrashBetsInput = {
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUpdateManyWithoutUserNestedInput
    coinflipsA?: CoinflipRoomUpdateManyWithoutPlayerANestedInput
    coinflipsB?: CoinflipRoomUpdateManyWithoutPlayerBNestedInput
    doubleBets?: DoubleBetUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutCrashBetsInput = {
    id?: IntFieldUpdateOperationsInput | number
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUncheckedUpdateManyWithoutUserNestedInput
    coinflipsA?: CoinflipRoomUncheckedUpdateManyWithoutPlayerANestedInput
    coinflipsB?: CoinflipRoomUncheckedUpdateManyWithoutPlayerBNestedInput
    doubleBets?: DoubleBetUncheckedUpdateManyWithoutUserNestedInput
  }

  export type DoubleBetCreateWithoutRoundInput = {
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
    user: UserCreateNestedOneWithoutDoubleBetsInput
  }

  export type DoubleBetUncheckedCreateWithoutRoundInput = {
    id?: number
    userId: number
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type DoubleBetCreateOrConnectWithoutRoundInput = {
    where: DoubleBetWhereUniqueInput
    create: XOR<DoubleBetCreateWithoutRoundInput, DoubleBetUncheckedCreateWithoutRoundInput>
  }

  export type DoubleBetCreateManyRoundInputEnvelope = {
    data: DoubleBetCreateManyRoundInput | DoubleBetCreateManyRoundInput[]
  }

  export type DoubleBetUpsertWithWhereUniqueWithoutRoundInput = {
    where: DoubleBetWhereUniqueInput
    update: XOR<DoubleBetUpdateWithoutRoundInput, DoubleBetUncheckedUpdateWithoutRoundInput>
    create: XOR<DoubleBetCreateWithoutRoundInput, DoubleBetUncheckedCreateWithoutRoundInput>
  }

  export type DoubleBetUpdateWithWhereUniqueWithoutRoundInput = {
    where: DoubleBetWhereUniqueInput
    data: XOR<DoubleBetUpdateWithoutRoundInput, DoubleBetUncheckedUpdateWithoutRoundInput>
  }

  export type DoubleBetUpdateManyWithWhereWithoutRoundInput = {
    where: DoubleBetScalarWhereInput
    data: XOR<DoubleBetUpdateManyMutationInput, DoubleBetUncheckedUpdateManyWithoutRoundInput>
  }

  export type DoubleRoundCreateWithoutBetsInput = {
    result: string
    seed: string
    createdAt?: Date | string
  }

  export type DoubleRoundUncheckedCreateWithoutBetsInput = {
    id?: number
    result: string
    seed: string
    createdAt?: Date | string
  }

  export type DoubleRoundCreateOrConnectWithoutBetsInput = {
    where: DoubleRoundWhereUniqueInput
    create: XOR<DoubleRoundCreateWithoutBetsInput, DoubleRoundUncheckedCreateWithoutBetsInput>
  }

  export type UserCreateWithoutDoubleBetsInput = {
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionCreateNestedManyWithoutUserInput
    coinflipsA?: CoinflipRoomCreateNestedManyWithoutPlayerAInput
    coinflipsB?: CoinflipRoomCreateNestedManyWithoutPlayerBInput
    crashBets?: CrashBetCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutDoubleBetsInput = {
    id?: number
    telegramId: string
    username?: string | null
    balanceTon?: Decimal | DecimalJsLike | number | string
    createdAt?: Date | string
    updatedAt?: Date | string
    transactions?: TransactionUncheckedCreateNestedManyWithoutUserInput
    coinflipsA?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerAInput
    coinflipsB?: CoinflipRoomUncheckedCreateNestedManyWithoutPlayerBInput
    crashBets?: CrashBetUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutDoubleBetsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutDoubleBetsInput, UserUncheckedCreateWithoutDoubleBetsInput>
  }

  export type DoubleRoundUpsertWithoutBetsInput = {
    update: XOR<DoubleRoundUpdateWithoutBetsInput, DoubleRoundUncheckedUpdateWithoutBetsInput>
    create: XOR<DoubleRoundCreateWithoutBetsInput, DoubleRoundUncheckedCreateWithoutBetsInput>
    where?: DoubleRoundWhereInput
  }

  export type DoubleRoundUpdateToOneWithWhereWithoutBetsInput = {
    where?: DoubleRoundWhereInput
    data: XOR<DoubleRoundUpdateWithoutBetsInput, DoubleRoundUncheckedUpdateWithoutBetsInput>
  }

  export type DoubleRoundUpdateWithoutBetsInput = {
    result?: StringFieldUpdateOperationsInput | string
    seed?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleRoundUncheckedUpdateWithoutBetsInput = {
    id?: IntFieldUpdateOperationsInput | number
    result?: StringFieldUpdateOperationsInput | string
    seed?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUpsertWithoutDoubleBetsInput = {
    update: XOR<UserUpdateWithoutDoubleBetsInput, UserUncheckedUpdateWithoutDoubleBetsInput>
    create: XOR<UserCreateWithoutDoubleBetsInput, UserUncheckedCreateWithoutDoubleBetsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutDoubleBetsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutDoubleBetsInput, UserUncheckedUpdateWithoutDoubleBetsInput>
  }

  export type UserUpdateWithoutDoubleBetsInput = {
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUpdateManyWithoutUserNestedInput
    coinflipsA?: CoinflipRoomUpdateManyWithoutPlayerANestedInput
    coinflipsB?: CoinflipRoomUpdateManyWithoutPlayerBNestedInput
    crashBets?: CrashBetUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutDoubleBetsInput = {
    id?: IntFieldUpdateOperationsInput | number
    telegramId?: StringFieldUpdateOperationsInput | string
    username?: NullableStringFieldUpdateOperationsInput | string | null
    balanceTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    transactions?: TransactionUncheckedUpdateManyWithoutUserNestedInput
    coinflipsA?: CoinflipRoomUncheckedUpdateManyWithoutPlayerANestedInput
    coinflipsB?: CoinflipRoomUncheckedUpdateManyWithoutPlayerBNestedInput
    crashBets?: CrashBetUncheckedUpdateManyWithoutUserNestedInput
  }

  export type TransactionCreateManyUserInput = {
    id?: number
    kind: string
    amountTon: Decimal | DecimalJsLike | number | string
    ref?: string | null
    createdAt?: Date | string
  }

  export type CoinflipRoomCreateManyPlayerAInput = {
    id?: number
    stakeTon: Decimal | DecimalJsLike | number | string
    playerBId?: number | null
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
  }

  export type CoinflipRoomCreateManyPlayerBInput = {
    id?: number
    stakeTon: Decimal | DecimalJsLike | number | string
    playerAId: number
    seedA?: string | null
    seedB?: string | null
    winnerId?: number | null
    status?: string
    createdAt?: Date | string
    finishedAt?: Date | string | null
  }

  export type CrashBetCreateManyUserInput = {
    id?: number
    roundId: number
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type DoubleBetCreateManyUserInput = {
    id?: number
    roundId: number
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type TransactionUpdateWithoutUserInput = {
    kind?: StringFieldUpdateOperationsInput | string
    amountTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    ref?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TransactionUncheckedUpdateWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    kind?: StringFieldUpdateOperationsInput | string
    amountTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    ref?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TransactionUncheckedUpdateManyWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    kind?: StringFieldUpdateOperationsInput | string
    amountTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    ref?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CoinflipRoomUpdateWithoutPlayerAInput = {
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    playerB?: UserUpdateOneWithoutCoinflipsBNestedInput
  }

  export type CoinflipRoomUncheckedUpdateWithoutPlayerAInput = {
    id?: IntFieldUpdateOperationsInput | number
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    playerBId?: NullableIntFieldUpdateOperationsInput | number | null
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CoinflipRoomUncheckedUpdateManyWithoutPlayerAInput = {
    id?: IntFieldUpdateOperationsInput | number
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    playerBId?: NullableIntFieldUpdateOperationsInput | number | null
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CoinflipRoomUpdateWithoutPlayerBInput = {
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    playerA?: UserUpdateOneRequiredWithoutCoinflipsANestedInput
  }

  export type CoinflipRoomUncheckedUpdateWithoutPlayerBInput = {
    id?: IntFieldUpdateOperationsInput | number
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    playerAId?: IntFieldUpdateOperationsInput | number
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CoinflipRoomUncheckedUpdateManyWithoutPlayerBInput = {
    id?: IntFieldUpdateOperationsInput | number
    stakeTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    playerAId?: IntFieldUpdateOperationsInput | number
    seedA?: NullableStringFieldUpdateOperationsInput | string | null
    seedB?: NullableStringFieldUpdateOperationsInput | string | null
    winnerId?: NullableIntFieldUpdateOperationsInput | number | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type CrashBetUpdateWithoutUserInput = {
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    round?: CrashRoundUpdateOneRequiredWithoutBetsNestedInput
  }

  export type CrashBetUncheckedUpdateWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    roundId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CrashBetUncheckedUpdateManyWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    roundId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleBetUpdateWithoutUserInput = {
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    round?: DoubleRoundUpdateOneRequiredWithoutBetsNestedInput
  }

  export type DoubleBetUncheckedUpdateWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    roundId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleBetUncheckedUpdateManyWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    roundId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CrashBetCreateManyRoundInput = {
    id?: number
    userId: number
    betTon: Decimal | DecimalJsLike | number | string
    cashoutAt?: Decimal | DecimalJsLike | number | string | null
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type CrashBetUpdateWithoutRoundInput = {
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutCrashBetsNestedInput
  }

  export type CrashBetUncheckedUpdateWithoutRoundInput = {
    id?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CrashBetUncheckedUpdateManyWithoutRoundInput = {
    id?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    cashoutAt?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleBetCreateManyRoundInput = {
    id?: number
    userId: number
    betTon: Decimal | DecimalJsLike | number | string
    color: string
    winTon?: Decimal | DecimalJsLike | number | string | null
    createdAt?: Date | string
  }

  export type DoubleBetUpdateWithoutRoundInput = {
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutDoubleBetsNestedInput
  }

  export type DoubleBetUncheckedUpdateWithoutRoundInput = {
    id?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DoubleBetUncheckedUpdateManyWithoutRoundInput = {
    id?: IntFieldUpdateOperationsInput | number
    userId?: IntFieldUpdateOperationsInput | number
    betTon?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    color?: StringFieldUpdateOperationsInput | string
    winTon?: NullableDecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Aliases for legacy arg types
   */
    /**
     * @deprecated Use UserCountOutputTypeDefaultArgs instead
     */
    export type UserCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CrashRoundCountOutputTypeDefaultArgs instead
     */
    export type CrashRoundCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CrashRoundCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use DoubleRoundCountOutputTypeDefaultArgs instead
     */
    export type DoubleRoundCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = DoubleRoundCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use UserDefaultArgs instead
     */
    export type UserArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserDefaultArgs<ExtArgs>
    /**
     * @deprecated Use PriceDefaultArgs instead
     */
    export type PriceArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = PriceDefaultArgs<ExtArgs>
    /**
     * @deprecated Use TransactionDefaultArgs instead
     */
    export type TransactionArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = TransactionDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CoinflipRoomDefaultArgs instead
     */
    export type CoinflipRoomArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CoinflipRoomDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CrashRoundDefaultArgs instead
     */
    export type CrashRoundArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CrashRoundDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CrashBetDefaultArgs instead
     */
    export type CrashBetArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CrashBetDefaultArgs<ExtArgs>
    /**
     * @deprecated Use DoubleRoundDefaultArgs instead
     */
    export type DoubleRoundArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = DoubleRoundDefaultArgs<ExtArgs>
    /**
     * @deprecated Use DoubleBetDefaultArgs instead
     */
    export type DoubleBetArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = DoubleBetDefaultArgs<ExtArgs>

  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}