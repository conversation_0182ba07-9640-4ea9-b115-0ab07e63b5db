import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import jwt from 'jsonwebtoken';
import { parse } from 'url';
import { logger } from '../utils/logger';
import config from '../config';

interface AuthenticatedWebSocket extends WebSocket {
  userId?: number;
  telegramId?: string;
  namespace?: string;
}

interface WSMessage {
  type: string;
  data: any;
}

// Store connected clients by namespace
const clients = {
  crash: new Set<AuthenticatedWebSocket>(),
  double: new Set<AuthenticatedWebSocket>(),
  coinflip: new Set<AuthenticatedWebSocket>()
};

/**
 * Setup WebSocket server with namespaces
 */
export function setupWebSockets(wss: WebSocketServer): void {
  wss.on('connection', (ws: AuthenticatedWebSocket, request: IncomingMessage) => {
    const url = parse(request.url || '', true);
    const pathname = url.pathname || '';
    
    // Determine namespace from path
    let namespace: string | null = null;
    if (pathname.startsWith('/crash')) {
      namespace = 'crash';
    } else if (pathname.startsWith('/double')) {
      namespace = 'double';
    } else if (pathname.startsWith('/coinflip')) {
      namespace = 'coinflip';
    }
    
    if (!namespace) {
      logger.warn(`Invalid WebSocket namespace: ${pathname}`);
      ws.close(1008, 'Invalid namespace');
      return;
    }
    
    ws.namespace = namespace;
    
    // Authenticate user
    const token = url.query.token as string;
    if (!token) {
      logger.warn('WebSocket connection without token');
      ws.close(1008, 'Authentication required');
      return;
    }
    
    try {
      const decoded = jwt.verify(token, config.JWT_SECRET) as any;
      ws.userId = decoded.userId;
      ws.telegramId = decoded.telegramId;
      
      // Add to namespace clients
      clients[namespace as keyof typeof clients].add(ws);
      
      logger.debug(`User ${ws.telegramId} connected to ${namespace} namespace`);
      
      // Send welcome message
      sendMessage(ws, {
        type: 'CONNECTED',
        data: {
          namespace,
          timestamp: new Date().toISOString()
        }
      });
      
    } catch (error) {
      logger.warn('Invalid WebSocket token:', error);
      ws.close(1008, 'Invalid token');
      return;
    }
    
    // Handle messages
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString()) as WSMessage;
        handleMessage(ws, message);
      } catch (error) {
        logger.error('Error parsing WebSocket message:', error);
        sendError(ws, 'Invalid message format');
      }
    });
    
    // Handle disconnect
    ws.on('close', () => {
      if (ws.namespace) {
        clients[ws.namespace as keyof typeof clients].delete(ws);
        logger.debug(`User ${ws.telegramId} disconnected from ${ws.namespace} namespace`);
      }
    });
    
    // Handle errors
    ws.on('error', (error) => {
      logger.error('WebSocket error:', error);
    });
  });
  
  logger.info('WebSocket server setup complete');
}

/**
 * Handle incoming WebSocket messages
 */
function handleMessage(ws: AuthenticatedWebSocket, message: WSMessage): void {
  const { type, data } = message;
  
  logger.debug(`WebSocket message from ${ws.telegramId}: ${type}`);
  
  switch (ws.namespace) {
    case 'crash':
      handleCrashMessage(ws, type, data);
      break;
    case 'double':
      handleDoubleMessage(ws, type, data);
      break;
    case 'coinflip':
      handleCoinflipMessage(ws, type, data);
      break;
    default:
      sendError(ws, 'Unknown namespace');
  }
}

/**
 * Handle crash namespace messages
 */
function handleCrashMessage(ws: AuthenticatedWebSocket, type: string, data: any): void {
  switch (type) {
    case 'PLACE_BET':
      // This would integrate with CrashEngine
      // For now, just acknowledge
      sendMessage(ws, {
        type: 'BET_PLACED',
        data: { success: true }
      });
      break;
      
    case 'CASH_OUT':
      // This would integrate with CrashEngine
      sendMessage(ws, {
        type: 'CASHED_OUT',
        data: { success: true }
      });
      break;
      
    default:
      sendError(ws, `Unknown crash message type: ${type}`);
  }
}

/**
 * Handle double namespace messages
 */
function handleDoubleMessage(ws: AuthenticatedWebSocket, type: string, data: any): void {
  switch (type) {
    case 'PLACE_BET':
      // This would integrate with DoubleEngine
      sendMessage(ws, {
        type: 'BET_PLACED',
        data: { success: true }
      });
      break;
      
    default:
      sendError(ws, `Unknown double message type: ${type}`);
  }
}

/**
 * Handle coinflip namespace messages
 */
function handleCoinflipMessage(ws: AuthenticatedWebSocket, type: string, data: any): void {
  switch (type) {
    case 'JOIN_ROOM':
      // This would integrate with CoinflipEngine
      sendMessage(ws, {
        type: 'ROOM_JOINED',
        data: { success: true }
      });
      break;
      
    default:
      sendError(ws, `Unknown coinflip message type: ${type}`);
  }
}

/**
 * Send message to a specific WebSocket
 */
function sendMessage(ws: AuthenticatedWebSocket, message: WSMessage): void {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({
      ...message,
      timestamp: new Date().toISOString()
    }));
  }
}

/**
 * Send error message to a specific WebSocket
 */
function sendError(ws: AuthenticatedWebSocket, error: string): void {
  sendMessage(ws, {
    type: 'ERROR',
    data: { error }
  });
}

/**
 * Broadcast message to all clients in a namespace
 */
export function broadcastToNamespace(namespace: keyof typeof clients, message: WSMessage): void {
  const namespaceClients = clients[namespace];
  
  namespaceClients.forEach(ws => {
    sendMessage(ws, message);
  });
  
  logger.debug(`Broadcasted ${message.type} to ${namespaceClients.size} clients in ${namespace} namespace`);
}

/**
 * Send message to specific user in a namespace
 */
export function sendToUser(namespace: keyof typeof clients, userId: number, message: WSMessage): void {
  const namespaceClients = clients[namespace];
  
  namespaceClients.forEach(ws => {
    if (ws.userId === userId) {
      sendMessage(ws, message);
    }
  });
}

/**
 * Get connected clients count for a namespace
 */
export function getClientsCount(namespace: keyof typeof clients): number {
  return clients[namespace].size;
}
