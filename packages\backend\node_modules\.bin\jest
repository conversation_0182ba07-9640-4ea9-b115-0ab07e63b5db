#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/jest@29.7.0_@types+node@20.17.57/node_modules/jest/bin/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/jest@29.7.0_@types+node@20.17.57/node_modules/jest/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/jest@29.7.0_@types+node@20.17.57/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/jest@29.7.0_@types+node@20.17.57/node_modules/jest/bin/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/jest@29.7.0_@types+node@20.17.57/node_modules/jest/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/jest@29.7.0_@types+node@20.17.57/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jest/bin/jest.js" "$@"
else
  exec node  "$basedir/../jest/bin/jest.js" "$@"
fi
