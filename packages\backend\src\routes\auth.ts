import { Router } from 'express';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { prisma } from '../database';
import config from '../config';
import { logger } from '../utils/logger';

const router = Router();

/**
 * Verify Telegram WebApp init data
 */
function verifyTelegramAuth(initData: string): any {
  // Development mode: allow test hash
  if (config.NODE_ENV === 'development' && initData.includes('hash=test_hash')) {
    const urlParams = new URLSearchParams(initData);
    const userParam = urlParams.get('user');
    if (userParam) {
      return JSON.parse(decodeURIComponent(userParam));
    }
  }

  const urlParams = new URLSearchParams(initData);
  const hash = urlParams.get('hash');
  urlParams.delete('hash');

  // Sort parameters
  const dataCheckString = Array.from(urlParams.entries())
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');

  // Create secret key
  const secretKey = crypto
    .createHmac('sha256', 'WebAppData')
    .update(config.TELEGRAM_BOT_TOKEN)
    .digest();

  // Calculate hash
  const calculatedHash = crypto
    .createHmac('sha256', secretKey)
    .update(dataCheckString)
    .digest('hex');

  if (calculatedHash !== hash) {
    throw new Error('Invalid hash');
  }

  // Parse user data
  const userParam = urlParams.get('user');
  if (!userParam) {
    throw new Error('No user data');
  }

  return JSON.parse(userParam);
}

/**
 * POST /api/auth/login
 * Authenticate user with Telegram WebApp data
 */
router.post('/login', async (req, res) => {
  try {
    const { initData } = req.body;
    
    if (!initData) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing initData' 
      });
    }
    
    // Verify Telegram auth
    const telegramUser = verifyTelegramAuth(initData);
    
    // Find or create user
    let user = await prisma.user.findUnique({
      where: { telegramId: telegramUser.id.toString() }
    });
    
    if (!user) {
      user = await prisma.user.create({
        data: {
          telegramId: telegramUser.id.toString(),
          username: telegramUser.username,
        }
      });
      
      logger.info(`New user registered: ${user.telegramId}`);
    } else {
      // Update username if changed
      if (user.username !== telegramUser.username) {
        user = await prisma.user.update({
          where: { id: user.id },
          data: { username: telegramUser.username }
        });
      }
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        telegramId: user.telegramId 
      },
      config.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          telegramId: user.telegramId,
          username: user.username,
          balanceTon: user.balanceTon.toString(),
          createdAt: user.createdAt.toISOString(),
          updatedAt: user.updatedAt.toISOString(),
        },
        token
      }
    });
    
  } catch (error) {
    logger.error('Auth error:', error);
    res.status(401).json({ 
      success: false, 
      error: 'Authentication failed' 
    });
  }
});

/**
 * GET /api/auth/me
 * Get current user info
 */
router.get('/me', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        error: 'No token provided' 
      });
    }
    
    const decoded = jwt.verify(token, config.JWT_SECRET) as any;
    
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });
    
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        error: 'User not found' 
      });
    }
    
    res.json({
      success: true,
      data: {
        id: user.id,
        telegramId: user.telegramId,
        username: user.username,
        balanceTon: user.balanceTon.toString(),
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
      }
    });
    
  } catch (error) {
    logger.error('Auth verification error:', error);
    res.status(401).json({ 
      success: false, 
      error: 'Invalid token' 
    });
  }
});

export default router;
