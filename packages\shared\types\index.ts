// User types
export interface User {
  id: number;
  telegramId: string;
  username?: string;
  balanceTon: string;
  createdAt: string;
  updatedAt: string;
}

// Transaction types
export type TransactionKind = 
  | 'DEPOSIT_GIFT' 
  | 'DEPOSIT_TON' 
  | 'WITHDRAW_GIFT' 
  | 'GAME_WIN' 
  | 'GAME_LOSS';

export interface Transaction {
  id: number;
  kind: TransactionKind;
  amountTon: string;
  ref?: string;
  userId?: number;
  createdAt: string;
}

// Gift types
export interface GiftModel {
  modelKey: string;
  name: string;
  description?: string;
  imageUrl?: string;
  minTon: string;
}

export interface Gift {
  uuid: string;
  modelKey: string;
  ownerUsername?: string;
  receivedAt: string;
}

// Game types
export type GameType = 'CRASH' | 'COINFLIP' | 'DOUBLE';

export type CoinflipRoomStatus = 'OPEN' | 'FULL' | 'FINISHED' | 'EXPIRED';

export interface CoinflipRoom {
  id: number;
  stakeTon: string;
  playerA: User;
  playerB?: User;
  winnerId?: number;
  status: CoinflipRoomStatus;
  createdAt: string;
  finishedAt?: string;
}

export type CrashRoundStatus = 'RUNNING' | 'CRASHED' | 'FINISHED';

export interface CrashRound {
  id: number;
  multiplier: string;
  status: CrashRoundStatus;
  createdAt: string;
  crashedAt?: string;
}

export interface CrashBet {
  id: number;
  roundId: number;
  userId: number;
  betTon: string;
  cashoutAt?: string;
  winTon?: string;
  createdAt: string;
}

export type DoubleColor = 'RED' | 'BLACK' | 'GREEN';

export interface DoubleRound {
  id: number;
  result: DoubleColor;
  createdAt: string;
}

export interface DoubleBet {
  id: number;
  roundId: number;
  userId: number;
  betTon: string;
  color: DoubleColor;
  winTon?: string;
  createdAt: string;
}

// WebSocket message types
export interface WSMessage {
  type: string;
  data: any;
  timestamp: string;
}

// Crash WebSocket messages
export interface CrashTickMessage extends WSMessage {
  type: 'CRASH_TICK';
  data: {
    roundId: number;
    multiplier: string;
    timestamp: string;
  };
}

export interface CrashCrashedMessage extends WSMessage {
  type: 'CRASH_CRASHED';
  data: {
    roundId: number;
    finalMultiplier: string;
    timestamp: string;
  };
}

export interface CrashNewRoundMessage extends WSMessage {
  type: 'CRASH_NEW_ROUND';
  data: {
    roundId: number;
    timestamp: string;
  };
}

export interface CrashBetMessage extends WSMessage {
  type: 'CRASH_BET';
  data: {
    roundId: number;
    betTon: string;
  };
}

export interface CrashCashoutMessage extends WSMessage {
  type: 'CRASH_CASHOUT';
  data: {
    roundId: number;
    multiplier: string;
  };
}

// Double WebSocket messages
export interface DoubleNewRoundMessage extends WSMessage {
  type: 'DOUBLE_NEW_ROUND';
  data: {
    roundId: number;
    timestamp: string;
  };
}

export interface DoubleResultMessage extends WSMessage {
  type: 'DOUBLE_RESULT';
  data: {
    roundId: number;
    result: DoubleColor;
    timestamp: string;
  };
}

export interface DoubleBetMessage extends WSMessage {
  type: 'DOUBLE_BET';
  data: {
    roundId: number;
    betTon: string;
    color: DoubleColor;
  };
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface BalanceResponse {
  balanceTon: string;
  transactions: Transaction[];
}

export interface DepositResponse {
  address: string;
  comment: string;
  amount: string;
}

export interface WithdrawResponse {
  transactionId: number;
  estimatedDelivery: string;
}

// Telegram WebApp types
export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
}

export interface TelegramWebAppInitData {
  user?: TelegramUser;
  chat_instance?: string;
  chat_type?: string;
  auth_date: number;
  hash: string;
}
