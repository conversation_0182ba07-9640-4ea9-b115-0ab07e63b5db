const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// Test data
const testInitData = 'user=%7B%22id%22%3A123456789%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&auth_date=1701234567&hash=test_hash';

async function testAPI() {
  try {
    console.log('🧪 Testing PEPE CAS API...\n');

    // Test 1: Login
    console.log('1. Testing login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      initData: testInitData
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Login successful');
      console.log('User:', loginResponse.data.data.user.username);
      console.log('Balance:', loginResponse.data.data.user.balanceTon, 'TON');
      
      const token = loginResponse.data.data.token;
      
      // Set authorization header for subsequent requests
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Test 2: Get balance
      console.log('\n2. Testing balance...');
      const balanceResponse = await axios.get(`${API_BASE}/balance`);
      
      if (balanceResponse.data.success) {
        console.log('✅ Balance fetch successful');
        console.log('Balance:', balanceResponse.data.data.balanceTon, 'TON');
        console.log('Transactions:', balanceResponse.data.data.transactions.length);
      }
      
      // Test 3: Get coinflip rooms
      console.log('\n3. Testing coinflip rooms...');
      const roomsResponse = await axios.get(`${API_BASE}/coinflip/rooms`);
      
      if (roomsResponse.data.success) {
        console.log('✅ Coinflip rooms fetch successful');
        console.log('Open rooms:', roomsResponse.data.data.length);
      }
      
      // Test 4: Create coinflip room
      console.log('\n4. Testing coinflip room creation...');
      const createRoomResponse = await axios.post(`${API_BASE}/coinflip/rooms`, {
        stakeTon: '5.0'
      });
      
      if (createRoomResponse.data.success) {
        console.log('✅ Coinflip room created successfully');
        console.log('Room ID:', createRoomResponse.data.data.id);
        console.log('Stake:', createRoomResponse.data.data.stakeTon, 'TON');
      }
      
      // Test 5: Check balance after room creation
      console.log('\n5. Checking balance after room creation...');
      const balanceAfterResponse = await axios.get(`${API_BASE}/balance`);
      
      if (balanceAfterResponse.data.success) {
        console.log('✅ Balance after room creation');
        console.log('New balance:', balanceAfterResponse.data.data.balanceTon, 'TON');
      }
      
    } else {
      console.log('❌ Login failed:', loginResponse.data.error);
    }
    
  } catch (error) {
    console.error('❌ API Test failed:', error.response?.data || error.message);
  }
}

// Test health endpoint
async function testHealth() {
  try {
    const response = await axios.get('http://localhost:3000/health');
    console.log('🏥 Health check:', response.data);
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
  }
}

async function runTests() {
  await testHealth();
  console.log('');
  await testAPI();
}

runTests();
