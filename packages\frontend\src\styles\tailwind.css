@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Casino-specific styles */
@layer components {
  .casino-bg {
    @apply bg-gradient-to-br from-casino-darker via-casino-dark to-black;
  }
  
  .casino-card {
    @apply bg-casino-dark/80 backdrop-blur-sm border border-white/10 rounded-lg;
  }
  
  .casino-button {
    @apply bg-gradient-to-r from-casino-gold to-yellow-500 text-black font-bold py-2 px-4 rounded-lg hover:from-yellow-500 hover:to-casino-gold transition-all duration-200 shadow-lg hover:shadow-xl;
  }
  
  .casino-button-red {
    @apply bg-gradient-to-r from-casino-red to-red-600 text-white font-bold py-2 px-4 rounded-lg hover:from-red-600 hover:to-casino-red transition-all duration-200 shadow-lg hover:shadow-xl;
  }
  
  .casino-button-green {
    @apply bg-gradient-to-r from-casino-green to-green-500 text-black font-bold py-2 px-4 rounded-lg hover:from-green-500 hover:to-casino-green transition-all duration-200 shadow-lg hover:shadow-xl;
  }
  
  .neon-text {
    @apply text-casino-green;
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }
  
  .neon-border {
    @apply border-casino-green;
    box-shadow: 0 0 5px currentColor, inset 0 0 5px currentColor;
  }
  
  .crash-multiplier {
    @apply text-4xl font-mono font-bold;
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
  }
  
  .roulette-red {
    @apply bg-gradient-to-br from-red-500 to-red-700 text-white;
  }
  
  .roulette-black {
    @apply bg-gradient-to-br from-gray-800 to-black text-white;
  }
  
  .roulette-green {
    @apply bg-gradient-to-br from-green-500 to-green-700 text-white;
  }
}

/* Telegram WebApp specific styles */
@layer utilities {
  .tg-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  .tg-viewport {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }
}

/* Animations */
@keyframes coinflip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(180deg); }
}

.coin-flip {
  animation: coinflip 1s ease-in-out;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
