import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '../database';
import config from '../config';
import { logger } from '../utils/logger';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: number;
        telegramId: string;
        username?: string;
      };
    }
  }
}

/**
 * Middleware to authenticate user via JWT token
 */
export async function authenticateUser(req: Request, res: Response, next: NextFunction) {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'No token provided'
      });
    }
    
    const decoded = jwt.verify(token, config.JWT_SECRET) as any;
    
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }
    
    req.user = {
      id: user.id,
      telegramId: user.telegramId,
      username: user.username || undefined
    };
    
    next();
    
  } catch (error) {
    logger.error('Authentication error:', error);
    res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  }
}

/**
 * Middleware to authenticate admin user
 */
export async function authenticateAdmin(req: Request, res: Response, next: NextFunction) {
  try {
    await authenticateUser(req, res, () => {});
    
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }
    
    // Check if user is admin
    if (!config.ADMIN_TELEGRAM_IDS.includes(req.user.telegramId)) {
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }
    
    next();
    
  } catch (error) {
    logger.error('Admin authentication error:', error);
    res.status(403).json({
      success: false,
      error: 'Admin access denied'
    });
  }
}
