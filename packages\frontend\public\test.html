<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PEPE CAS Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
        }
        button {
            background: #00ff88;
            color: #000;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            background: #00cc6a;
        }
        .info {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐸 PEPE CAS Test</h1>
        <div class="info">
            <p>Это тестовая страница для проверки PEPE CAS без Telegram бота.</p>
            <p>Используется тестовый пользователь с ID: 123456789</p>
        </div>
        
        <button onclick="openApp()">Открыть PEPE CAS</button>
        
        <div class="info">
            <h3>Тестовые данные:</h3>
            <ul>
                <li>Пользователь: testuser</li>
                <li>Баланс: 100 TON</li>
                <li>Telegram ID: 123456789</li>
            </ul>
        </div>
    </div>

    <script>
        // Mock Telegram WebApp
        window.Telegram = {
            WebApp: {
                ready: () => console.log('Telegram WebApp ready'),
                expand: () => console.log('Telegram WebApp expanded'),
                initData: 'user=%7B%22id%22%3A123456789%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&auth_date=1701234567&hash=test_hash',
                initDataUnsafe: {
                    user: {
                        id: 123456789,
                        first_name: 'Test',
                        username: 'testuser'
                    },
                    auth_date: 1701234567,
                    hash: 'test_hash'
                }
            }
        };

        function openApp() {
            // Открываем основное приложение с мок-данными
            console.log('Opening PEPE CAS with test data...');
            console.log('Telegram WebApp mock:', window.Telegram.WebApp);
            window.location.href = '/';
        }
    </script>
</body>
</html>
