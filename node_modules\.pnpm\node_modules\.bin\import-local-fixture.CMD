@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\import-local@3.2.0\node_modules\import-local\fixtures\node_modules;C:\PEPE CAS\node_modules\.pnpm\import-local@3.2.0\node_modules\import-local\node_modules;C:\PEPE CAS\node_modules\.pnpm\import-local@3.2.0\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\import-local@3.2.0\node_modules\import-local\fixtures\node_modules;C:\PEPE CAS\node_modules\.pnpm\import-local@3.2.0\node_modules\import-local\node_modules;C:\PEPE CAS\node_modules\.pnpm\import-local@3.2.0\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\import-local\fixtures\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\import-local\fixtures\cli.js" %*
)
