import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import axios from 'axios';

interface User {
  id: number;
  telegramId: string;
  username?: string;
  balanceTon: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  initialize: () => Promise<void>;
  login: () => Promise<void>;
  logout: () => void;
  updateBalance: (balance: string) => void;
  clearError: () => void;
}

// Configure axios defaults
axios.defaults.baseURL = '/api';

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,
      error: null,

      initialize: async () => {
        try {
          const { token } = get();
          
          if (!token) {
            // Try to authenticate with Telegram WebApp
            await get().login();
            return;
          }

          // Set token in axios headers
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          // Verify token and get user info
          const response = await axios.get('/auth/me');
          
          if (response.data.success) {
            set({
              user: response.data.data,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            throw new Error('Invalid token');
          }
        } catch (error) {
          console.error('Auth initialization error:', error);
          // Try to login again
          await get().login();
        }
      },

      login: async () => {
        try {
          set({ isLoading: true, error: null });

          let initData = window.Telegram?.WebApp?.initData;

          // Fallback for development/testing
          if (!initData) {
            console.warn('No Telegram WebApp data, using test data');
            initData = 'user=%7B%22id%22%3A123456789%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&auth_date=1701234567&hash=test_hash';
          }

          // Send login request
          const response = await axios.post('/auth/login', {
            initData
          });

          if (response.data.success) {
            const { user, token } = response.data.data;
            
            // Set token in axios headers
            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            throw new Error(response.data.error || 'Login failed');
          }
        } catch (error: any) {
          console.error('Login error:', error);
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: error.response?.data?.error || error.message || 'Login failed'
          });
        }
      },

      logout: () => {
        // Clear axios headers
        delete axios.defaults.headers.common['Authorization'];
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        });
      },

      updateBalance: (balance: string) => {
        const { user } = get();
        if (user) {
          set({
            user: {
              ...user,
              balanceTon: balance
            }
          });
        }
      },

      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'pepe-cas-auth',
      partialize: (state) => ({
        token: state.token,
        user: state.user
      })
    }
  )
);
