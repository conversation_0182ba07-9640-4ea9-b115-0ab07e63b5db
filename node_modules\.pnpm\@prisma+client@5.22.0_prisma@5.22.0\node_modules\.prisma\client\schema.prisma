// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id         Int      @id @default(autoincrement())
  telegramId String   @unique
  username   String?
  balanceTon Decimal  @default(0)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  transactions Transaction[]
  coinflipsA   CoinflipRoom[] @relation("PlayerA")
  coinflipsB   CoinflipRoom[] @relation("PlayerB")
  crashBets    CrashBet[]
  doubleBets   DoubleBet[]

  @@map("users")
}

model Price {
  modelKey  String   @id
  minTon    Decimal
  updatedAt DateTime @updatedAt

  @@map("prices")
}

model Transaction {
  id        Int      @id @default(autoincrement())
  kind      String // DEPOSIT_GIFT, DEPOSIT_TON, WITHDRAW_GIFT, GAME_WIN, GAME_LOSS
  amountTon Decimal
  ref       String? // gift UUID or tx hash or game reference
  userId    Int?
  user      User?    @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())

  @@map("transactions")
}

model CoinflipRoom {
  id         Int       @id @default(autoincrement())
  stakeTon   Decimal
  playerAId  Int
  playerA    User      @relation("PlayerA", fields: [playerAId], references: [id])
  playerBId  Int?
  playerB    User?     @relation("PlayerB", fields: [playerBId], references: [id])
  seedA      String?
  seedB      String?
  winnerId   Int?
  status     String    @default("OPEN") // OPEN, FULL, FINISHED, EXPIRED
  createdAt  DateTime  @default(now())
  finishedAt DateTime?

  @@map("coinflip_rooms")
}

model CrashRound {
  id         Int       @id @default(autoincrement())
  multiplier Decimal
  seed       String
  status     String    @default("RUNNING") // RUNNING, CRASHED, FINISHED
  createdAt  DateTime  @default(now())
  crashedAt  DateTime?

  bets CrashBet[]

  @@map("crash_rounds")
}

model CrashBet {
  id        Int        @id @default(autoincrement())
  roundId   Int
  round     CrashRound @relation(fields: [roundId], references: [id])
  userId    Int
  user      User       @relation(fields: [userId], references: [id])
  betTon    Decimal
  cashoutAt Decimal?
  winTon    Decimal?
  createdAt DateTime   @default(now())

  @@unique([roundId, userId])
  @@map("crash_bets")
}

model DoubleRound {
  id        Int      @id @default(autoincrement())
  result    String // RED, BLACK, GREEN
  seed      String
  createdAt DateTime @default(now())

  bets DoubleBet[]

  @@map("double_rounds")
}

model DoubleBet {
  id        Int         @id @default(autoincrement())
  roundId   Int
  round     DoubleRound @relation(fields: [roundId], references: [id])
  userId    Int
  user      User        @relation(fields: [userId], references: [id])
  betTon    Decimal
  color     String // RED, BLACK, GREEN
  winTon    Decimal?
  createdAt DateTime    @default(now())

  @@unique([roundId, userId, color])
  @@map("double_bets")
}
