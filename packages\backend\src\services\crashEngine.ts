import { EventEmitter } from 'events';
import { prisma } from '../database';
import config from '../config';
import { logger } from '../utils/logger';
import { RNG } from '../utils/rng';

interface CrashBet {
  userId: number;
  betTon: string;
  cashedOut: boolean;
  cashoutMultiplier?: string;
}

export class CrashEngine extends EventEmitter {
  private isRunning = false;
  private currentRound: any = null;
  private currentBets: Map<number, CrashBet> = new Map();
  private roundInterval: NodeJS.Timeout | null = null;
  private tickInterval: NodeJS.Timeout | null = null;
  private startTime: number = 0;
  private crashMultiplier: number = 1.0;
  
  constructor() {
    super();
  }
  
  /**
   * Start the crash engine
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Crash engine is already running');
      return;
    }
    
    this.isRunning = true;
    logger.info('Starting crash engine...');
    
    // Start first round
    await this.startNewRound();
    
    logger.info(`Crash engine started with ${config.CRASH_INTERVAL}s interval`);
  }
  
  /**
   * Stop the crash engine
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }
    
    this.isRunning = false;
    
    if (this.roundInterval) {
      clearInterval(this.roundInterval);
      this.roundInterval = null;
    }
    
    if (this.tickInterval) {
      clearInterval(this.tickInterval);
      this.tickInterval = null;
    }
    
    logger.info('Crash engine stopped');
  }
  
  /**
   * Start a new crash round
   */
  private async startNewRound(): Promise<void> {
    try {
      // Generate crash multiplier
      const seed = RNG.generateSeed();
      this.crashMultiplier = RNG.generateCrashMultiplier(seed);
      
      // Create round in database
      this.currentRound = await prisma.crashRound.create({
        data: {
          multiplier: this.crashMultiplier.toString(),
          seed,
          status: 'RUNNING'
        }
      });
      
      // Reset state
      this.currentBets.clear();
      this.startTime = Date.now();
      
      logger.info(`New crash round ${this.currentRound.id} started (will crash at ${this.crashMultiplier}x)`);
      
      // Emit new round event
      this.emit('newRound', {
        roundId: this.currentRound.id,
        timestamp: new Date().toISOString()
      });
      
      // Start ticking
      this.startTicking();
      
    } catch (error) {
      logger.error('Error starting new crash round:', error);
    }
  }
  
  /**
   * Start the multiplier ticking
   */
  private startTicking(): void {
    this.tickInterval = setInterval(() => {
      const elapsed = (Date.now() - this.startTime) / 1000;
      const currentMultiplier = this.calculateCurrentMultiplier(elapsed);
      
      // Emit tick event
      this.emit('tick', {
        roundId: this.currentRound.id,
        multiplier: currentMultiplier.toFixed(2),
        timestamp: new Date().toISOString()
      });
      
      // Check if we should crash
      if (currentMultiplier >= this.crashMultiplier) {
        this.crashRound();
      }
    }, 100); // 10 FPS
  }
  
  /**
   * Calculate current multiplier based on elapsed time
   */
  private calculateCurrentMultiplier(elapsedSeconds: number): number {
    // Simple linear growth: 1.0x + 0.1x per second
    // You can implement more sophisticated curves here
    return 1.0 + (elapsedSeconds * 0.1);
  }
  
  /**
   * Crash the current round
   */
  private async crashRound(): Promise<void> {
    try {
      if (this.tickInterval) {
        clearInterval(this.tickInterval);
        this.tickInterval = null;
      }
      
      // Update round status
      await prisma.crashRound.update({
        where: { id: this.currentRound.id },
        data: {
          status: 'CRASHED',
          crashedAt: new Date()
        }
      });
      
      logger.info(`Crash round ${this.currentRound.id} crashed at ${this.crashMultiplier}x`);
      
      // Process all bets
      await this.processBets();
      
      // Emit crash event
      this.emit('crashed', {
        roundId: this.currentRound.id,
        finalMultiplier: this.crashMultiplier.toFixed(2),
        timestamp: new Date().toISOString()
      });
      
      // Schedule next round
      setTimeout(() => {
        if (this.isRunning) {
          this.startNewRound();
        }
      }, config.CRASH_INTERVAL * 1000);
      
    } catch (error) {
      logger.error('Error crashing round:', error);
    }
  }
  
  /**
   * Process all bets for the crashed round
   */
  private async processBets(): Promise<void> {
    try {
      const betPromises = Array.from(this.currentBets.entries()).map(
        ([userId, bet]) => this.processBet(userId, bet)
      );
      
      await Promise.all(betPromises);
      
    } catch (error) {
      logger.error('Error processing bets:', error);
    }
  }
  
  /**
   * Process a single bet
   */
  private async processBet(userId: number, bet: CrashBet): Promise<void> {
    try {
      const betAmount = parseFloat(bet.betTon);
      let winAmount = 0;
      
      if (bet.cashedOut && bet.cashoutMultiplier) {
        // User cashed out in time
        winAmount = betAmount * parseFloat(bet.cashoutMultiplier);
      }
      // If not cashed out, winAmount remains 0 (loss)
      
      // Update bet in database
      await prisma.crashBet.update({
        where: {
          roundId_userId: {
            roundId: this.currentRound.id,
            userId
          }
        },
        data: {
          cashoutAt: bet.cashoutMultiplier || null,
          winTon: winAmount > 0 ? winAmount.toString() : null
        }
      });
      
      // If user won, credit their balance
      if (winAmount > 0) {
        await prisma.$transaction(async (tx) => {
          await tx.user.update({
            where: { id: userId },
            data: {
              balanceTon: {
                increment: winAmount.toString()
              }
            }
          });
          
          await tx.transaction.create({
            data: {
              kind: 'GAME_WIN',
              amountTon: winAmount.toString(),
              ref: `crash_round_${this.currentRound.id}`,
              userId
            }
          });
        });
      }
      
    } catch (error) {
      logger.error(`Error processing bet for user ${userId}:`, error);
    }
  }
  
  /**
   * Place a bet in the current round
   */
  async placeBet(userId: number, betTon: string): Promise<boolean> {
    try {
      if (!this.currentRound || this.currentRound.status !== 'RUNNING') {
        return false;
      }
      
      if (this.currentBets.has(userId)) {
        return false; // User already has a bet
      }
      
      const betAmount = parseFloat(betTon);
      if (betAmount <= 0) {
        return false;
      }
      
      // Check user balance and deduct bet
      const result = await prisma.$transaction(async (tx) => {
        const user = await tx.user.findUnique({
          where: { id: userId }
        });
        
        if (!user || user.balanceTon.lt(betTon)) {
          throw new Error('Insufficient balance');
        }
        
        // Deduct bet from balance
        await tx.user.update({
          where: { id: userId },
          data: {
            balanceTon: {
              decrement: betTon
            }
          }
        });
        
        // Create bet record
        await tx.crashBet.create({
          data: {
            roundId: this.currentRound.id,
            userId,
            betTon
          }
        });
        
        // Create transaction record
        await tx.transaction.create({
          data: {
            kind: 'GAME_LOSS',
            amountTon: `-${betTon}`,
            ref: `crash_bet_${this.currentRound.id}`,
            userId
          }
        });
        
        return true;
      });
      
      // Add to current bets
      this.currentBets.set(userId, {
        userId,
        betTon,
        cashedOut: false
      });
      
      logger.debug(`User ${userId} placed bet of ${betTon} TON in round ${this.currentRound.id}`);
      
      return true;
      
    } catch (error) {
      logger.error(`Error placing bet for user ${userId}:`, error);
      return false;
    }
  }
  
  /**
   * Cash out a bet
   */
  async cashOut(userId: number): Promise<boolean> {
    try {
      const bet = this.currentBets.get(userId);
      
      if (!bet || bet.cashedOut) {
        return false;
      }
      
      const elapsed = (Date.now() - this.startTime) / 1000;
      const currentMultiplier = this.calculateCurrentMultiplier(elapsed);
      
      // Check if still before crash
      if (currentMultiplier >= this.crashMultiplier) {
        return false; // Too late, already crashed
      }
      
      // Mark as cashed out
      bet.cashedOut = true;
      bet.cashoutMultiplier = currentMultiplier.toFixed(2);
      
      logger.debug(`User ${userId} cashed out at ${currentMultiplier.toFixed(2)}x`);
      
      return true;
      
    } catch (error) {
      logger.error(`Error cashing out for user ${userId}:`, error);
      return false;
    }
  }
}
