import { Router } from 'express';
import { prisma } from '../database';
import { authenticateUser } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = Router();

/**
 * GET /api/balance
 * Get user balance and recent transactions
 */
router.get('/', authenticateUser, async (req, res) => {
  try {
    const userId = req.user!.id;
    
    // Get user with current balance
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Get recent transactions (last 50)
    const transactions = await prisma.transaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 50
    });
    
    res.json({
      success: true,
      data: {
        balanceTon: user.balanceTon.toString(),
        transactions: transactions.map(tx => ({
          id: tx.id,
          kind: tx.kind,
          amountTon: tx.amountTon.toString(),
          ref: tx.ref,
          createdAt: tx.createdAt.toISOString()
        }))
      }
    });
    
  } catch (error) {
    logger.error('Balance fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch balance'
    });
  }
});

/**
 * GET /api/balance/history
 * Get detailed transaction history with pagination
 */
router.get('/history', authenticateUser, async (req, res) => {
  try {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const kind = req.query.kind as string;
    
    const skip = (page - 1) * limit;
    
    const where: any = { userId };
    if (kind) {
      where.kind = kind;
    }
    
    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.transaction.count({ where })
    ]);
    
    res.json({
      success: true,
      data: {
        transactions: transactions.map(tx => ({
          id: tx.id,
          kind: tx.kind,
          amountTon: tx.amountTon.toString(),
          ref: tx.ref,
          createdAt: tx.createdAt.toISOString()
        })),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
    
  } catch (error) {
    logger.error('Transaction history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch transaction history'
    });
  }
});

export default router;
