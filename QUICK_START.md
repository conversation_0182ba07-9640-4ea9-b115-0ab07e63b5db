# 🐸 PEPE CAS - Быстрый старт

## ✅ Что уже готово

Проект полностью настроен и запущен! Все основные компоненты работают:

### 🎮 Игры
- **Crash Game** - ставки на растущий множитель
- **Double Game** - рулетка (красное/черное/зеленое)  
- **Coinflip PvP** - дуэли между игроками

### 💰 Экономика
- Система балансов (TON)
- Пополнение через NFT подарки
- Вывод средств подарками
- История транзакций

### 🔧 Техническая часть
- Backend API (Node.js + Express + SQLite)
- Frontend (React + TypeScript + Vite)
- WebSocket для реального времени
- Провайдер справедливости (RNG)

## 🚀 Как запустить

### 1. Сервер уже запущен!
```bash
# Backend: http://localhost:3000
# Frontend: http://localhost:5173
```

### 2. Тестирование
Откройте в браузере: http://localhost:5173/test.html

Это тестовая страница с мок-данными Telegram WebApp.

### 3. Тестовый пользователь
- **Username**: testuser
- **Telegram ID**: 123456789  
- **Баланс**: 95 TON (потратил 5 TON на создание комнаты)

## 🎯 Что можно протестировать

### В браузере (http://localhost:5173/test.html)
1. Нажмите "Открыть PEPE CAS"
2. Приложение автоматически авторизует тестового пользователя
3. Попробуйте все игры и функции

### Игры
- **Crash**: Делайте ставки и забирайте до краха
- **Double**: Ставьте на цвета (красный 2x, черный 2x, зеленый 14x)
- **Coinflip**: Создавайте комнаты для PvP дуэлей

### Операции
- Просмотр баланса и истории
- Пополнение (симуляция)
- Вывод средств (симуляция)

## 🔧 API тестирование

Запустите тест API:
```bash
cd packages/backend
node scripts/testApi.js
```

## 📱 Telegram Bot

Ваш бот: **@pepe_cas_bot** (токен: 7918026591:AAHrRX3Z1tLtO7aE_gPblArhJMGTv5kixvI)

### Настройка бота:
1. Откройте @BotFather в Telegram
2. Найдите вашего бота
3. Установите команды:
   ```
   start - Запустить казино
   help - Помощь
   balance - Проверить баланс
   ```
4. Настройте Web App URL: `https://your-domain.com`

## 🌐 Деплой в продакшн

### Быстрый деплой через Docker:
```bash
cd packages/infra
docker-compose up -d
```

### Или ручной деплой:
```bash
# Сборка
pnpm build

# Запуск
cd packages/backend
npm start
```

## 🎮 Особенности игр

### Crash Game
- Множитель растет от 1.00x
- Забирайте до краха
- Провайдер справедливости (Rayleigh distribution)

### Double Game  
- Раунды по 15 секунд
- Красное/Черное: 2x (47% каждое)
- Зеленое: 14x (6%)

### Coinflip PvP
- Игрок A создает комнату
- Игрок B присоединяется
- Победитель получает 1.8x (комиссия 10%)

## 🔒 Безопасность

- JWT аутентификация
- Rate limiting
- Провайдер справедливости
- Валидация всех входных данных

## 📊 Мониторинг

- Health check: http://localhost:3000/health
- Логи в консоли (development mode)
- WebSocket статус в реальном времени

## 🐛 Отладка

### Проблемы с подключением:
1. Проверьте, что сервер запущен на порту 3000
2. Проверьте, что фронтенд запущен на порту 5173
3. Откройте консоль браузера для ошибок

### Проблемы с играми:
1. Проверьте WebSocket подключение
2. Убедитесь, что у пользователя достаточно средств
3. Проверьте логи сервера

## 🎉 Готово!

Ваше казино PEPE CAS полностью функционально и готово к использованию!

Для продакшн деплоя замените:
- SQLite на PostgreSQL
- Добавьте Redis для WebSocket scaling
- Настройте SSL сертификаты
- Обновите CORS настройки
