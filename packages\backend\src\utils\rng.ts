import crypto from 'crypto';
import config from '../config';

/**
 * Secure random number generator utilities for casino games
 */
export class RNG {
  /**
   * Generate a cryptographically secure random float between 0 and 1
   */
  static random(): number {
    const buffer = crypto.randomBytes(4);
    return buffer.readUInt32BE(0) / 0x100000000;
  }

  /**
   * Generate a random integer between min and max (inclusive)
   */
  static randomInt(min: number, max: number): number {
    return Math.floor(this.random() * (max - min + 1)) + min;
  }

  /**
   * Generate a random seed string
   */
  static generateSeed(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate crash multiplier using Rayleigh distribution
   * Based on bustabit's provably fair algorithm
   */
  static generateCrashMultiplier(seed?: string): number {
    const hash = seed ? crypto.createHash('sha256').update(seed).digest('hex') : this.generateSeed();
    
    // Convert first 8 characters of hash to number
    const hashInt = parseInt(hash.substring(0, 8), 16);
    const e = hashInt / Math.pow(2, 32);
    
    // Use Rayleigh distribution with sigma parameter
    const sigma = config.CRASH_SIGMA;
    const multiplier = Math.sqrt(-2 * sigma * sigma * Math.log(1 - e));
    
    // Ensure minimum multiplier of 1.00
    return Math.max(1.0, Math.round(multiplier * 100) / 100);
  }

  /**
   * Generate double game result (RED, BLACK, GREEN)
   */
  static generateDoubleResult(seed?: string): 'RED' | 'BLACK' | 'GREEN' {
    const random = seed ? this.seededRandom(seed) : this.random();
    
    if (random < config.DOUBLE_GREEN_PROBABILITY) {
      return 'GREEN';
    } else if (random < config.DOUBLE_GREEN_PROBABILITY + config.DOUBLE_RED_PROBABILITY) {
      return 'RED';
    } else {
      return 'BLACK';
    }
  }

  /**
   * Generate coinflip result (0 or 1) from two seeds
   */
  static generateCoinflipResult(seedA: string, seedB: string): 0 | 1 {
    const combined = crypto.createHash('sha256').update(seedA + seedB).digest('hex');
    const result = parseInt(combined.substring(0, 1), 16);
    return result % 2 as 0 | 1;
  }

  /**
   * Generate seeded random number (for provably fair games)
   */
  private static seededRandom(seed: string): number {
    const hash = crypto.createHash('sha256').update(seed).digest('hex');
    const hashInt = parseInt(hash.substring(0, 8), 16);
    return hashInt / Math.pow(2, 32);
  }

  /**
   * Verify game result using seed (for provably fair verification)
   */
  static verifyCrashResult(seed: string, expectedMultiplier: number): boolean {
    const calculatedMultiplier = this.generateCrashMultiplier(seed);
    return Math.abs(calculatedMultiplier - expectedMultiplier) < 0.01;
  }

  /**
   * Verify double result using seed
   */
  static verifyDoubleResult(seed: string, expectedResult: 'RED' | 'BLACK' | 'GREEN'): boolean {
    const calculatedResult = this.generateDoubleResult(seed);
    return calculatedResult === expectedResult;
  }

  /**
   * Verify coinflip result using both seeds
   */
  static verifyCoinflipResult(seedA: string, seedB: string, expectedResult: 0 | 1): boolean {
    const calculatedResult = this.generateCoinflipResult(seedA, seedB);
    return calculatedResult === expectedResult;
  }
}

export default RNG;
