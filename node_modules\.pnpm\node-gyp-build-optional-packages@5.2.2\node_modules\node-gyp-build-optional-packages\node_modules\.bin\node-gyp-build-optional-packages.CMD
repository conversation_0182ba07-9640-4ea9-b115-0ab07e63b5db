@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\node-gyp-build-optional-packages@5.2.2\node_modules\node-gyp-build-optional-packages\node_modules;C:\PEPE CAS\node_modules\.pnpm\node-gyp-build-optional-packages@5.2.2\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\node-gyp-build-optional-packages@5.2.2\node_modules\node-gyp-build-optional-packages\node_modules;C:\PEPE CAS\node_modules\.pnpm\node-gyp-build-optional-packages@5.2.2\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin.js" %*
)
