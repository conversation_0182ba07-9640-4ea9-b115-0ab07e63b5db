import { EventEmitter } from 'events';
import { prisma } from '../database';
import config from '../config';
import { logger } from '../utils/logger';
import { RNG } from '../utils/rng';

type DoubleColor = 'RED' | 'BLACK' | 'GREEN';

interface DoubleBet {
  userId: number;
  betTon: string;
  color: DoubleColor;
}

export class DoubleEngine extends EventEmitter {
  private isRunning = false;
  private currentRound: any = null;
  private currentBets: DoubleBet[] = [];
  private roundInterval: NodeJS.Timeout | null = null;
  private bettingPhase = true;
  private bettingTimeLeft = 0;
  
  constructor() {
    super();
  }
  
  /**
   * Start the double engine
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Double engine is already running');
      return;
    }
    
    this.isRunning = true;
    logger.info('Starting double engine...');
    
    // Start first round
    await this.startNewRound();
    
    logger.info(`Double engine started with ${config.DOUBLE_INTERVAL}s interval`);
  }
  
  /**
   * Stop the double engine
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }
    
    this.isRunning = false;
    
    if (this.roundInterval) {
      clearInterval(this.roundInterval);
      this.roundInterval = null;
    }
    
    logger.info('Double engine stopped');
  }
  
  /**
   * Start a new double round
   */
  private async startNewRound(): Promise<void> {
    try {
      // Generate result
      const seed = RNG.generateSeed();
      const result = RNG.generateDoubleResult(seed);
      
      // Create round in database
      this.currentRound = await prisma.doubleRound.create({
        data: {
          result,
          seed
        }
      });
      
      // Reset state
      this.currentBets = [];
      this.bettingPhase = true;
      this.bettingTimeLeft = config.DOUBLE_INTERVAL;
      
      logger.info(`New double round ${this.currentRound.id} started (result: ${result})`);
      
      // Emit new round event
      this.emit('newRound', {
        roundId: this.currentRound.id,
        bettingTimeLeft: this.bettingTimeLeft,
        timestamp: new Date().toISOString()
      });
      
      // Start countdown
      this.startCountdown();
      
    } catch (error) {
      logger.error('Error starting new double round:', error);
    }
  }
  
  /**
   * Start the betting countdown
   */
  private startCountdown(): void {
    this.roundInterval = setInterval(() => {
      this.bettingTimeLeft--;
      
      // Emit countdown event
      this.emit('countdown', {
        roundId: this.currentRound.id,
        timeLeft: this.bettingTimeLeft,
        timestamp: new Date().toISOString()
      });
      
      // Check if betting phase is over
      if (this.bettingTimeLeft <= 0) {
        this.endBettingPhase();
      }
    }, 1000);
  }
  
  /**
   * End betting phase and reveal result
   */
  private async endBettingPhase(): Promise<void> {
    try {
      if (this.roundInterval) {
        clearInterval(this.roundInterval);
        this.roundInterval = null;
      }
      
      this.bettingPhase = false;
      
      logger.info(`Double round ${this.currentRound.id} betting ended, revealing result: ${this.currentRound.result}`);
      
      // Process all bets
      await this.processBets();
      
      // Emit result event
      this.emit('result', {
        roundId: this.currentRound.id,
        result: this.currentRound.result,
        timestamp: new Date().toISOString()
      });
      
      // Schedule next round
      setTimeout(() => {
        if (this.isRunning) {
          this.startNewRound();
        }
      }, 3000); // 3 second pause between rounds
      
    } catch (error) {
      logger.error('Error ending betting phase:', error);
    }
  }
  
  /**
   * Process all bets for the round
   */
  private async processBets(): Promise<void> {
    try {
      const betPromises = this.currentBets.map(bet => this.processBet(bet));
      await Promise.all(betPromises);
      
    } catch (error) {
      logger.error('Error processing double bets:', error);
    }
  }
  
  /**
   * Process a single bet
   */
  private async processBet(bet: DoubleBet): Promise<void> {
    try {
      const betAmount = parseFloat(bet.betTon);
      let winAmount = 0;
      
      // Calculate win amount based on color and result
      if (bet.color === this.currentRound.result) {
        if (bet.color === 'GREEN') {
          winAmount = betAmount * 14; // 14x for green
        } else {
          winAmount = betAmount * 2; // 2x for red/black
        }
      }
      // If colors don't match, winAmount remains 0 (loss)
      
      // Update bet in database
      await prisma.doubleBet.update({
        where: {
          roundId_userId_color: {
            roundId: this.currentRound.id,
            userId: bet.userId,
            color: bet.color
          }
        },
        data: {
          winTon: winAmount > 0 ? winAmount.toString() : null
        }
      });
      
      // If user won, credit their balance
      if (winAmount > 0) {
        await prisma.$transaction(async (tx) => {
          await tx.user.update({
            where: { id: bet.userId },
            data: {
              balanceTon: {
                increment: winAmount.toString()
              }
            }
          });
          
          await tx.transaction.create({
            data: {
              kind: 'GAME_WIN',
              amountTon: winAmount.toString(),
              ref: `double_round_${this.currentRound.id}`,
              userId: bet.userId
            }
          });
        });
        
        logger.debug(`User ${bet.userId} won ${winAmount} TON on ${bet.color}`);
      }
      
    } catch (error) {
      logger.error(`Error processing double bet for user ${bet.userId}:`, error);
    }
  }
  
  /**
   * Place a bet in the current round
   */
  async placeBet(userId: number, betTon: string, color: DoubleColor): Promise<boolean> {
    try {
      if (!this.currentRound || !this.bettingPhase) {
        return false;
      }
      
      // Check if user already has a bet on this color
      const existingBet = this.currentBets.find(
        bet => bet.userId === userId && bet.color === color
      );
      
      if (existingBet) {
        return false; // User already has a bet on this color
      }
      
      const betAmount = parseFloat(betTon);
      if (betAmount <= 0) {
        return false;
      }
      
      // Check user balance and deduct bet
      const result = await prisma.$transaction(async (tx) => {
        const user = await tx.user.findUnique({
          where: { id: userId }
        });
        
        if (!user || user.balanceTon.lt(betTon)) {
          throw new Error('Insufficient balance');
        }
        
        // Deduct bet from balance
        await tx.user.update({
          where: { id: userId },
          data: {
            balanceTon: {
              decrement: betTon
            }
          }
        });
        
        // Create bet record
        await tx.doubleBet.create({
          data: {
            roundId: this.currentRound.id,
            userId,
            betTon,
            color
          }
        });
        
        // Create transaction record
        await tx.transaction.create({
          data: {
            kind: 'GAME_LOSS',
            amountTon: `-${betTon}`,
            ref: `double_bet_${this.currentRound.id}`,
            userId
          }
        });
        
        return true;
      });
      
      // Add to current bets
      this.currentBets.push({
        userId,
        betTon,
        color
      });
      
      logger.debug(`User ${userId} placed bet of ${betTon} TON on ${color} in round ${this.currentRound.id}`);
      
      return true;
      
    } catch (error) {
      logger.error(`Error placing double bet for user ${userId}:`, error);
      return false;
    }
  }
  
  /**
   * Get current round info
   */
  getCurrentRound(): any {
    return {
      roundId: this.currentRound?.id,
      bettingPhase: this.bettingPhase,
      timeLeft: this.bettingTimeLeft
    };
  }
}
