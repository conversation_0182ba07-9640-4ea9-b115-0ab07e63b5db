import { Router } from 'express';
import { prisma } from '../database';
import { authenticateUser } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = Router();

/**
 * GET /api/coinflip/rooms
 * Get coinflip rooms
 */
router.get('/coinflip/rooms', authenticateUser, async (req, res) => {
  try {
    const status = req.query.status as string;
    
    const where: any = {};
    if (status) {
      where.status = status;
    }
    
    const rooms = await prisma.coinflipRoom.findMany({
      where,
      include: {
        playerA: true,
        playerB: true
      },
      orderBy: { createdAt: 'desc' },
      take: 50
    });
    
    res.json({
      success: true,
      data: rooms.map(room => ({
        id: room.id,
        stakeTon: room.stakeTon.toString(),
        playerA: {
          id: room.playerA.id,
          username: room.playerA.username,
          telegramId: room.playerA.telegramId
        },
        playerB: room.playerB ? {
          id: room.playerB.id,
          username: room.playerB.username,
          telegramId: room.playerB.telegramId
        } : null,
        winnerId: room.winnerId,
        status: room.status,
        createdAt: room.createdAt.toISOString(),
        finishedAt: room.finishedAt?.toISOString()
      }))
    });
    
  } catch (error) {
    logger.error('Coinflip rooms fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch coinflip rooms'
    });
  }
});

/**
 * POST /api/coinflip/rooms
 * Create a new coinflip room
 */
router.post('/coinflip/rooms', authenticateUser, async (req, res) => {
  try {
    const userId = req.user!.id;
    const { stakeTon } = req.body;
    
    if (!stakeTon || parseFloat(stakeTon) <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid stake amount'
      });
    }
    
    // Check user balance
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user || user.balanceTon.lt(stakeTon)) {
      return res.status(400).json({
        success: false,
        error: 'Insufficient balance'
      });
    }
    
    // Create room and deduct balance in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Deduct stake from user balance
      await tx.user.update({
        where: { id: userId },
        data: {
          balanceTon: {
            decrement: stakeTon
          }
        }
      });
      
      // Create transaction record
      await tx.transaction.create({
        data: {
          kind: 'GAME_LOSS',
          amountTon: `-${stakeTon}`,
          ref: 'coinflip_stake',
          userId
        }
      });
      
      // Create coinflip room
      const room = await tx.coinflipRoom.create({
        data: {
          stakeTon,
          playerAId: userId,
          status: 'OPEN'
        },
        include: {
          playerA: true
        }
      });
      
      return room;
    });
    
    res.json({
      success: true,
      data: {
        id: result.id,
        stakeTon: result.stakeTon.toString(),
        playerA: {
          id: result.playerA.id,
          username: result.playerA.username,
          telegramId: result.playerA.telegramId
        },
        status: result.status,
        createdAt: result.createdAt.toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Coinflip room creation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create coinflip room'
    });
  }
});

/**
 * POST /api/coinflip/rooms/:id/join
 * Join a coinflip room
 */
router.post('/coinflip/rooms/:id/join', authenticateUser, async (req, res) => {
  try {
    const userId = req.user!.id;
    const roomId = parseInt(req.params.id);
    
    if (isNaN(roomId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid room ID'
      });
    }
    
    // Get room
    const room = await prisma.coinflipRoom.findUnique({
      where: { id: roomId },
      include: { playerA: true }
    });
    
    if (!room) {
      return res.status(404).json({
        success: false,
        error: 'Room not found'
      });
    }
    
    if (room.status !== 'OPEN') {
      return res.status(400).json({
        success: false,
        error: 'Room is not open'
      });
    }
    
    if (room.playerAId === userId) {
      return res.status(400).json({
        success: false,
        error: 'Cannot join your own room'
      });
    }
    
    // Check user balance
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user || user.balanceTon.lt(room.stakeTon)) {
      return res.status(400).json({
        success: false,
        error: 'Insufficient balance'
      });
    }
    
    // Join room logic will be implemented in coinflipEngine
    // For now, just return success
    res.json({
      success: true,
      message: 'Joining room...'
    });
    
  } catch (error) {
    logger.error('Coinflip join error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to join room'
    });
  }
});

export default router;
