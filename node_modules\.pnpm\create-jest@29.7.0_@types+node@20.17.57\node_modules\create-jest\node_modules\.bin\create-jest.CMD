@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\create-jest@29.7.0_@types+node@20.17.57\node_modules\create-jest\bin\node_modules;C:\PEPE CAS\node_modules\.pnpm\create-jest@29.7.0_@types+node@20.17.57\node_modules\create-jest\node_modules;C:\PEPE CAS\node_modules\.pnpm\create-jest@29.7.0_@types+node@20.17.57\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\create-jest@29.7.0_@types+node@20.17.57\node_modules\create-jest\bin\node_modules;C:\PEPE CAS\node_modules\.pnpm\create-jest@29.7.0_@types+node@20.17.57\node_modules\create-jest\node_modules;C:\PEPE CAS\node_modules\.pnpm\create-jest@29.7.0_@types+node@20.17.57\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\create-jest.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\create-jest.js" %*
)
