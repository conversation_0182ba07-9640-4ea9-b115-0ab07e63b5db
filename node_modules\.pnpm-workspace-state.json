{"lastValidatedTimestamp": 1748799676166, "projects": {"C:\\PEPE CAS": {"name": "pepe-cas", "version": "1.0.0"}, "C:\\PEPE CAS\\packages\\backend": {"name": "@pepe-cas/backend", "version": "1.0.0"}, "C:\\PEPE CAS\\packages\\frontend": {"name": "@pepe-cas/frontend", "version": "1.0.0"}, "C:\\PEPE CAS\\packages\\shared": {"name": "@pepe-cas/shared", "version": "1.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*"]}, "filteredInstall": false}