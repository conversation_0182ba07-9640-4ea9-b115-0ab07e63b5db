@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\ts-jest@29.3.4_@babel+core@_6748ad135ca5e78561c2870afa9596b1\node_modules\ts-jest\node_modules;C:\PEPE CAS\node_modules\.pnpm\ts-jest@29.3.4_@babel+core@_6748ad135ca5e78561c2870afa9596b1\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\ts-jest@29.3.4_@babel+core@_6748ad135ca5e78561c2870afa9596b1\node_modules\ts-jest\node_modules;C:\PEPE CAS\node_modules\.pnpm\ts-jest@29.3.4_@babel+core@_6748ad135ca5e78561c2870afa9596b1\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\ts-jest\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\ts-jest\cli.js" %*
)
