version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: pepe-cas-postgres
    environment:
      POSTGRES_DB: pepe_cas
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for BullMQ and caching
  redis:
    image: redis:7-alpine
    container_name: pepe-cas-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: pepe-cas-backend
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: postgresql://postgres:${POSTGRES_PASSWORD:-password}@postgres:5432/pepe_cas
      REDIS_URL: redis://:${REDIS_PASSWORD:-password}@redis:6379
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
      TELEGRAM_BOT_SECRET: ${TELEGRAM_BOT_SECRET}
      JWT_SECRET: ${JWT_SECRET}
      TONNEL_API_URL: ${TONNEL_API_URL:-https://api.tonnel.io/v1}
      PORTAL_API_URL: ${PORTAL_API_URL:-https://portal.gg/api}
      ADMIN_TELEGRAM_IDS: ${ADMIN_TELEGRAM_IDS}
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (Nginx serving static files)
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: pepe-cas-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Watchtower for auto-updates
  watchtower:
    image: containrrr/watchtower
    container_name: pepe-cas-watchtower
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      WATCHTOWER_CLEANUP: true
      WATCHTOWER_POLL_INTERVAL: 3600 # Check every hour
      WATCHTOWER_INCLUDE_STOPPED: true
      WATCHTOWER_REVIVE_STOPPED: true
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: pepe-cas-network
