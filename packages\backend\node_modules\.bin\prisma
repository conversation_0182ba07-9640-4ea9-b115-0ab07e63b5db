#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/prisma@5.22.0/node_modules/prisma/build/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/prisma@5.22.0/node_modules/prisma/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/prisma@5.22.0/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/prisma@5.22.0/node_modules/prisma/build/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/prisma@5.22.0/node_modules/prisma/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/prisma@5.22.0/node_modules:/proc/cygdrive/c/PEPE CAS/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../prisma/build/index.js" "$@"
else
  exec node  "$basedir/../prisma/build/index.js" "$@"
fi
