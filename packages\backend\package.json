{"name": "@pepe-cas/backend", "version": "1.0.0", "description": "PEPE CAS Backend API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio"}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "@prisma/client": "^5.7.0", "prisma": "^5.7.0", "bullmq": "^4.15.0", "ioredis": "^5.3.2", "node-cron": "^3.0.3", "axios": "^1.6.2", "crypto": "^1.0.1", "jsonwebtoken": "^9.0.2", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "decimal.js": "^10.4.3", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/ws": "^8.5.10", "@types/cors": "^2.8.17", "@types/node": "^20.10.0", "@types/jsonwebtoken": "^9.0.5", "@types/jest": "^29.5.8", "tsx": "^4.6.0", "typescript": "^5.3.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}}