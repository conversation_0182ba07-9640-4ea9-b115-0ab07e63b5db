import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import cron from 'node-cron';

import config from './config';
import { logger } from './utils/logger';
import { prisma } from './database';

// Import routes
import authRoutes from './routes/auth';
import balanceRoutes from './routes/balance';
import gameRoutes from './routes/games';
import adminRoutes from './routes/admin';

// Import services
import { PriceUpdater } from './services/priceUpdater';
import { GiftWatcher } from './services/giftWatcher';
import { CrashEngine } from './services/crashEngine';
import { DoubleEngine } from './services/doubleEngine';

// Import WebSocket handlers
import { setupWebSockets } from './sockets';

const app = express();
const server = createServer(app);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://telegram.org"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: config.ALLOWED_ORIGINS,
  credentials: true,
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW,
  max: config.RATE_LIMIT_MAX,
  message: 'Too many requests from this IP, please try again later.',
});

const speedLimiter = slowDown({
  windowMs: config.RATE_LIMIT_WINDOW,
  delayAfter: Math.floor(config.RATE_LIMIT_MAX * 0.5),
  delayMs: 500,
});

app.use(limiter);
app.use(speedLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/balance', balanceRoutes);
app.use('/api', gameRoutes);
app.use('/api/admin', adminRoutes);

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    message: config.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Initialize services
async function initializeServices() {
  try {
    logger.info('Initializing services...');
    
    // Initialize database connection
    await prisma.$connect();
    logger.info('Database connected');
    
    // Initialize price updater
    const priceUpdater = new PriceUpdater();
    
    // Initialize gift watcher
    const giftWatcher = new GiftWatcher();
    await giftWatcher.start();
    
    // Initialize game engines
    const crashEngine = new CrashEngine();
    const doubleEngine = new DoubleEngine();
    
    await crashEngine.start();
    await doubleEngine.start();
    
    // Schedule price updates (daily at midnight)
    cron.schedule(config.PRICE_UPDATE_INTERVAL, async () => {
      logger.info('Running scheduled price update...');
      await priceUpdater.updateAllPrices();
    });
    
    // Initial price update
    await priceUpdater.updateAllPrices();
    
    logger.info('All services initialized successfully');
    
  } catch (error) {
    logger.error('Failed to initialize services:', error);
    process.exit(1);
  }
}

// Setup WebSocket server
const wss = new WebSocketServer({ server });
setupWebSockets(wss);

// Start server
async function startServer() {
  try {
    await initializeServices();
    
    server.listen(config.PORT, () => {
      logger.info(`🚀 Server running on port ${config.PORT}`);
      logger.info(`📱 Environment: ${config.NODE_ENV}`);
      logger.info(`🎰 PEPE CAS Backend is ready!`);
    });
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully...');
  server.close(async () => {
    await prisma.$disconnect();
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully...');
  server.close(async () => {
    await prisma.$disconnect();
    process.exit(0);
  });
});

// Start the server
startServer();
