{"name": "zustand", "description": "🐻 Bear necessities for state management in React", "private": false, "version": "4.5.7", "main": "./index.js", "types": "./index.d.ts", "typesVersions": {"<4.0": {"esm/*": ["ts3.4/*"], "*": ["ts3.4/*"]}}, "files": ["**"], "exports": {"./package.json": "./package.json", ".": {"react-native": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./esm/index.d.mts", "default": "./esm/index.mjs"}, "module": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./index.d.ts", "default": "./index.js"}}, "./vanilla": {"react-native": {"types": "./vanilla.d.ts", "default": "./vanilla.js"}, "import": {"types": "./esm/vanilla.d.mts", "default": "./esm/vanilla.mjs"}, "module": {"types": "./esm/vanilla.d.ts", "default": "./esm/vanilla.js"}, "default": {"types": "./vanilla.d.ts", "default": "./vanilla.js"}}, "./middleware": {"react-native": {"types": "./middleware.d.ts", "default": "./middleware.js"}, "import": {"types": "./esm/middleware.d.mts", "default": "./esm/middleware.mjs"}, "module": {"types": "./esm/middleware.d.ts", "default": "./esm/middleware.js"}, "default": {"types": "./middleware.d.ts", "default": "./middleware.js"}}, "./middleware/immer": {"react-native": {"types": "./middleware/immer.d.ts", "default": "./middleware/immer.js"}, "import": {"types": "./esm/middleware/immer.d.mts", "default": "./esm/middleware/immer.mjs"}, "module": {"types": "./esm/middleware/immer.d.ts", "default": "./esm/middleware/immer.js"}, "default": {"types": "./middleware/immer.d.ts", "default": "./middleware/immer.js"}}, "./shallow": {"react-native": {"types": "./shallow.d.ts", "default": "./shallow.js"}, "import": {"types": "./esm/shallow.d.mts", "default": "./esm/shallow.mjs"}, "module": {"types": "./esm/shallow.d.ts", "default": "./esm/shallow.js"}, "default": {"types": "./shallow.d.ts", "default": "./shallow.js"}}, "./vanilla/shallow": {"react-native": {"types": "./vanilla/shallow.d.ts", "default": "./vanilla/shallow.js"}, "import": {"types": "./esm/vanilla/shallow.d.mts", "default": "./esm/vanilla/shallow.mjs"}, "module": {"types": "./esm/vanilla/shallow.d.ts", "default": "./esm/vanilla/shallow.js"}, "default": {"types": "./vanilla/shallow.d.ts", "default": "./vanilla/shallow.js"}}, "./react/shallow": {"react-native": {"types": "./react/shallow.d.ts", "default": "./react/shallow.js"}, "import": {"types": "./esm/react/shallow.d.mts", "default": "./esm/react/shallow.mjs"}, "module": {"types": "./esm/react/shallow.d.ts", "default": "./esm/react/shallow.js"}, "default": {"types": "./react/shallow.d.ts", "default": "./react/shallow.js"}}, "./traditional": {"react-native": {"types": "./traditional.d.ts", "default": "./traditional.js"}, "import": {"types": "./esm/traditional.d.mts", "default": "./esm/traditional.mjs"}, "module": {"types": "./esm/traditional.d.ts", "default": "./esm/traditional.js"}, "default": {"types": "./traditional.d.ts", "default": "./traditional.js"}}, "./context": {"react-native": {"types": "./context.d.ts", "default": "./context.js"}, "import": {"types": "./esm/context.d.mts", "default": "./esm/context.mjs"}, "module": {"types": "./esm/context.d.ts", "default": "./esm/context.js"}, "default": {"types": "./context.d.ts", "default": "./context.js"}}}, "sideEffects": false, "engines": {"node": ">=12.7.0"}, "repository": {"type": "git", "url": "git+https://github.com/pmndrs/zustand.git"}, "keywords": ["react", "state", "manager", "management", "redux", "store"], "author": "<PERSON>", "contributors": ["<PERSON> (https://github.com/JeremyRH)", "<PERSON><PERSON> (https://github.com/dai-shi)"], "license": "MIT", "bugs": {"url": "https://github.com/pmndrs/zustand/issues"}, "homepage": "https://github.com/pmndrs/zustand", "packageManager": "pnpm@8.15.0", "dependencies": {"use-sync-external-store": "^1.2.2"}, "peerDependencies": {"@types/react": ">=16.8", "immer": ">=9.0.6", "react": ">=16.8"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "immer": {"optional": true}, "react": {"optional": true}}}