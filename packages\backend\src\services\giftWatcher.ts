import axios from 'axios';
import { prisma } from '../database';
import config from '../config';
import { logger } from '../utils/logger';
import { PriceUpdater } from './priceUpdater';

interface GiftInboxItem {
  uuid: string;
  modelKey: string;
  comment?: string;
  receivedAt: string;
  processed?: boolean;
}

export class GiftWatcher {
  private isRunning = false;
  private intervalId: NodeJS.Timeout | null = null;
  private priceUpdater: PriceUpdater;
  
  constructor() {
    this.priceUpdater = new PriceUpdater();
  }
  
  /**
   * Start the gift watcher
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Gift watcher is already running');
      return;
    }
    
    this.isRunning = true;
    logger.info('Starting gift watcher...');
    
    // Initial check
    await this.checkInbox();
    
    // Set up interval
    this.intervalId = setInterval(
      () => this.checkInbox(),
      config.GIFT_WATCHER_INTERVAL
    );
    
    logger.info(`Gift watcher started with ${config.GIFT_WATCHER_INTERVAL}ms interval`);
  }
  
  /**
   * Stop the gift watcher
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }
    
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    logger.info('Gift watcher stopped');
  }
  
  /**
   * Check inbox for new gifts
   */
  private async checkInbox(): Promise<void> {
    try {
      logger.debug('Checking gift inbox...');
      
      const gifts = await this.fetchInboxGifts();
      
      if (gifts.length === 0) {
        logger.debug('No new gifts found');
        return;
      }
      
      logger.info(`Found ${gifts.length} new gifts`);
      
      for (const gift of gifts) {
        await this.processGift(gift);
      }
      
    } catch (error) {
      logger.error('Error checking gift inbox:', error);
    }
  }
  
  /**
   * Fetch gifts from Tonnel inbox API
   */
  private async fetchInboxGifts(): Promise<GiftInboxItem[]> {
    try {
      const response = await axios.get(
        `${config.TONNEL_API_URL}/gifts/inbox`,
        {
          timeout: 15000,
          headers: {
            'User-Agent': 'PEPE-CAS/1.0'
          }
        }
      );
      
      if (!response.data || !Array.isArray(response.data.gifts)) {
        logger.warn('Invalid inbox response format');
        return [];
      }
      
      // Filter out already processed gifts
      const processedGifts = await this.getProcessedGiftUUIDs();
      
      return response.data.gifts.filter((gift: GiftInboxItem) => 
        !processedGifts.has(gift.uuid)
      );
      
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        logger.debug('No gifts in inbox (404)');
        return [];
      }
      
      throw error;
    }
  }
  
  /**
   * Get set of already processed gift UUIDs
   */
  private async getProcessedGiftUUIDs(): Promise<Set<string>> {
    try {
      const transactions = await prisma.transaction.findMany({
        where: {
          kind: 'DEPOSIT_GIFT',
          ref: { not: null }
        },
        select: { ref: true }
      });
      
      return new Set(
        transactions
          .map(tx => tx.ref)
          .filter((ref): ref is string => ref !== null)
      );
      
    } catch (error) {
      logger.error('Error fetching processed gifts:', error);
      return new Set();
    }
  }
  
  /**
   * Process a single gift
   */
  private async processGift(gift: GiftInboxItem): Promise<void> {
    try {
      logger.info(`Processing gift ${gift.uuid} (${gift.modelKey})`);
      
      // Extract username from comment
      const username = this.extractUsernameFromComment(gift.comment);
      
      if (!username) {
        logger.warn(`No username found in gift ${gift.uuid} comment: "${gift.comment}"`);
        return;
      }
      
      // Find user by username
      const user = await prisma.user.findFirst({
        where: { username }
      });
      
      if (!user) {
        logger.warn(`User not found for username: ${username}`);
        return;
      }
      
      // Get floor price for this gift model
      const floorPrice = await this.priceUpdater.getModelPrice(gift.modelKey);
      
      if (!floorPrice) {
        logger.warn(`No floor price found for model: ${gift.modelKey}`);
        return;
      }
      
      // Credit user balance and create transaction
      await prisma.$transaction(async (tx) => {
        // Update user balance
        await tx.user.update({
          where: { id: user.id },
          data: {
            balanceTon: {
              increment: floorPrice.toString()
            }
          }
        });
        
        // Create transaction record
        await tx.transaction.create({
          data: {
            kind: 'DEPOSIT_GIFT',
            amountTon: floorPrice.toString(),
            ref: gift.uuid,
            userId: user.id
          }
        });
      });
      
      logger.info(
        `Credited ${floorPrice} TON to user ${username} for gift ${gift.uuid}`
      );
      
    } catch (error) {
      logger.error(`Error processing gift ${gift.uuid}:`, error);
    }
  }
  
  /**
   * Extract username from gift comment
   * Expected format: "@username" or "username"
   */
  private extractUsernameFromComment(comment?: string): string | null {
    if (!comment) {
      return null;
    }
    
    // Remove @ symbol if present and trim whitespace
    const cleaned = comment.trim().replace(/^@/, '');
    
    // Basic validation: alphanumeric + underscore, 3-32 chars
    if (!/^[a-zA-Z0-9_]{3,32}$/.test(cleaned)) {
      return null;
    }
    
    return cleaned;
  }
}
