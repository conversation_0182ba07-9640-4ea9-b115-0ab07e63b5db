@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\msgpackr-extract@3.0.3\node_modules\msgpackr-extract\bin\node_modules;C:\PEPE CAS\node_modules\.pnpm\msgpackr-extract@3.0.3\node_modules\msgpackr-extract\node_modules;C:\PEPE CAS\node_modules\.pnpm\msgpackr-extract@3.0.3\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\msgpackr-extract@3.0.3\node_modules\msgpackr-extract\bin\node_modules;C:\PEPE CAS\node_modules\.pnpm\msgpackr-extract@3.0.3\node_modules\msgpackr-extract\node_modules;C:\PEPE CAS\node_modules\.pnpm\msgpackr-extract@3.0.3\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\msgpackr-extract@3.0.3\node_modules\msgpackr-extract\bin\download-prebuilds.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\msgpackr-extract@3.0.3\node_modules\msgpackr-extract\bin\download-prebuilds.js" %*
)
