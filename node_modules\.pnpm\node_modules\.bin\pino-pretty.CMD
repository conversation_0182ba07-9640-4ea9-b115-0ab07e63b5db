@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\pino-pretty@10.3.1\node_modules\pino-pretty\node_modules;C:\PEPE CAS\node_modules\.pnpm\pino-pretty@10.3.1\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\PEPE CAS\node_modules\.pnpm\pino-pretty@10.3.1\node_modules\pino-pretty\node_modules;C:\PEPE CAS\node_modules\.pnpm\pino-pretty@10.3.1\node_modules;C:\PEPE CAS\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\pino-pretty\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\pino-pretty\bin.js" %*
)
