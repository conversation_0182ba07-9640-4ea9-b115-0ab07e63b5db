import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // Server
  PORT: parseInt(process.env.PORT || '3000'),
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // Database
  DATABASE_URL: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/pepe_cas',
  
  // Redis
  REDIS_URL: process.env.REDIS_URL || 'redis://localhost:6379',
  
  // Telegram
  TELEGRAM_BOT_TOKEN: process.env.TELEGRAM_BOT_TOKEN || '',
  TELEGRAM_BOT_SECRET: process.env.TELEGRAM_BOT_SECRET || '',
  
  // TON
  TON_NETWORK: process.env.TON_NETWORK || 'testnet',
  TON_API_KEY: process.env.TON_API_KEY || '',
  
  // External APIs
  TONNEL_API_URL: process.env.TONNEL_API_URL || 'https://api.tonnel.io/v1',
  PORTAL_API_URL: process.env.PORTAL_API_URL || 'https://portal.gg/api',
  COINGECKO_API_URL: process.env.COINGECKO_API_URL || 'https://api.coingecko.com/api/v3',
  
  // Game Settings
  CRASH_INTERVAL: parseInt(process.env.CRASH_INTERVAL || '30'), // seconds
  DOUBLE_INTERVAL: parseInt(process.env.DOUBLE_INTERVAL || '15'), // seconds
  COINFLIP_TIMEOUT: parseInt(process.env.COINFLIP_TIMEOUT || '300'), // seconds
  
  // Security
  JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key',
  ADMIN_TELEGRAM_IDS: process.env.ADMIN_TELEGRAM_IDS?.split(',') || [],
  
  // Rate Limiting
  RATE_LIMIT_WINDOW: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
  RATE_LIMIT_MAX: parseInt(process.env.RATE_LIMIT_MAX || '100'),
  
  // Game Economics
  HOUSE_EDGE: parseFloat(process.env.HOUSE_EDGE || '0.02'), // 2%
  MAX_BET_PERCENTAGE: parseFloat(process.env.MAX_BET_PERCENTAGE || '0.2'), // 20% of bankroll
  
  // Double Game Probabilities
  DOUBLE_RED_PROBABILITY: parseFloat(process.env.DOUBLE_RED_PROBABILITY || '0.47'),
  DOUBLE_BLACK_PROBABILITY: parseFloat(process.env.DOUBLE_BLACK_PROBABILITY || '0.47'),
  DOUBLE_GREEN_PROBABILITY: parseFloat(process.env.DOUBLE_GREEN_PROBABILITY || '0.06'),
  
  // Crash Game Settings
  CRASH_SIGMA: parseFloat(process.env.CRASH_SIGMA || '1.5'), // Rayleigh distribution parameter
  
  // Gift Processing
  GIFT_WATCHER_INTERVAL: parseInt(process.env.GIFT_WATCHER_INTERVAL || '30000'), // 30 seconds
  PRICE_UPDATE_INTERVAL: process.env.PRICE_UPDATE_INTERVAL || '0 0 * * *', // Daily at midnight
  
  // CORS
  ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS?.split(',') || ['https://t.me'],
};

// Validation
if (!config.TELEGRAM_BOT_TOKEN) {
  throw new Error('TELEGRAM_BOT_TOKEN is required');
}

if (!config.JWT_SECRET || config.JWT_SECRET === 'your-secret-key') {
  console.warn('⚠️  Using default JWT_SECRET. Please set a secure secret in production!');
}

export default config;
