import { Router } from 'express';
import { prisma } from '../database';
import { authenticateAdmin } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = Router();

/**
 * GET /api/admin/stats
 * Get platform statistics
 */
router.get('/stats', authenticateAdmin, async (req, res) => {
  try {
    const [
      totalUsers,
      totalTransactions,
      totalVolume,
      activeRooms
    ] = await Promise.all([
      prisma.user.count(),
      prisma.transaction.count(),
      prisma.transaction.aggregate({
        _sum: { amountTon: true }
      }),
      prisma.coinflipRoom.count({
        where: { status: 'OPEN' }
      })
    ]);
    
    res.json({
      success: true,
      data: {
        totalUsers,
        totalTransactions,
        totalVolume: totalVolume._sum.amountTon?.toString() || '0',
        activeRooms,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Admin stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics'
    });
  }
});

/**
 * GET /api/admin/users
 * Get users list with pagination
 */
router.get('/users', authenticateAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const search = req.query.search as string;
    
    const skip = (page - 1) * limit;
    
    const where: any = {};
    if (search) {
      where.OR = [
        { username: { contains: search, mode: 'insensitive' } },
        { telegramId: { contains: search } }
      ];
    }
    
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.user.count({ where })
    ]);
    
    res.json({
      success: true,
      data: {
        users: users.map(user => ({
          id: user.id,
          telegramId: user.telegramId,
          username: user.username,
          balanceTon: user.balanceTon.toString(),
          createdAt: user.createdAt.toISOString()
        })),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
    
  } catch (error) {
    logger.error('Admin users fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users'
    });
  }
});

/**
 * POST /api/admin/users/:id/balance
 * Adjust user balance (admin only)
 */
router.post('/users/:id/balance', authenticateAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { amount, reason } = req.body;
    
    if (isNaN(userId) || !amount || !reason) {
      return res.status(400).json({
        success: false,
        error: 'Invalid parameters'
      });
    }
    
    const result = await prisma.$transaction(async (tx) => {
      // Update user balance
      const user = await tx.user.update({
        where: { id: userId },
        data: {
          balanceTon: {
            increment: amount
          }
        }
      });
      
      // Create transaction record
      await tx.transaction.create({
        data: {
          kind: parseFloat(amount) > 0 ? 'DEPOSIT_TON' : 'WITHDRAW_GIFT',
          amountTon: amount,
          ref: `admin_adjustment: ${reason}`,
          userId
        }
      });
      
      return user;
    });
    
    logger.info(`Admin ${req.user!.telegramId} adjusted balance for user ${userId}: ${amount} TON (${reason})`);
    
    res.json({
      success: true,
      data: {
        newBalance: result.balanceTon.toString()
      }
    });
    
  } catch (error) {
    logger.error('Admin balance adjustment error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to adjust balance'
    });
  }
});

export default router;
