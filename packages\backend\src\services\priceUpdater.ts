import axios from 'axios';
import { prisma } from '../database';
import config from '../config';
import { logger } from '../utils/logger';

interface FloorPriceResponse {
  modelKey: string;
  floorPrice: number;
  currency: 'TON';
}

export class PriceUpdater {
  private readonly tonnelApiUrl = config.TONNEL_API_URL;
  private readonly portalApiUrl = config.PORTAL_API_URL;
  
  /**
   * Update floor prices for all gift models
   */
  async updateAllPrices(): Promise<void> {
    try {
      logger.info('Starting price update...');
      
      // Get list of known gift models (you might want to maintain this list)
      const knownModels = [
        'delicious_cake',
        'green_star',
        'blue_star',
        'red_star',
        'golden_star',
        'premium_gift',
        // Add more models as needed
      ];
      
      const updatePromises = knownModels.map(modelKey => 
        this.updateModelPrice(modelKey)
      );
      
      const results = await Promise.allSettled(updatePromises);
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      logger.info(`Price update completed: ${successful} successful, ${failed} failed`);
      
    } catch (error) {
      logger.error('Price update error:', error);
    }
  }
  
  /**
   * Update floor price for a specific gift model
   */
  async updateModelPrice(modelKey: string): Promise<void> {
    try {
      let floorPrice: number | null = null;
      
      // Try Tonnel API first
      try {
        floorPrice = await this.fetchTonnelPrice(modelKey);
      } catch (error) {
        logger.warn(`Tonnel API failed for ${modelKey}:`, error);
      }
      
      // Fallback to Portal API if Tonnel fails
      if (floorPrice === null) {
        try {
          floorPrice = await this.fetchPortalPrice(modelKey);
        } catch (error) {
          logger.warn(`Portal API failed for ${modelKey}:`, error);
        }
      }
      
      if (floorPrice === null) {
        logger.error(`Failed to fetch price for ${modelKey} from all sources`);
        return;
      }
      
      // Update database
      await prisma.price.upsert({
        where: { modelKey },
        update: { 
          minTon: floorPrice.toString(),
          updatedAt: new Date()
        },
        create: {
          modelKey,
          minTon: floorPrice.toString()
        }
      });
      
      logger.debug(`Updated price for ${modelKey}: ${floorPrice} TON`);
      
    } catch (error) {
      logger.error(`Error updating price for ${modelKey}:`, error);
    }
  }
  
  /**
   * Fetch floor price from Tonnel API
   */
  private async fetchTonnelPrice(modelKey: string): Promise<number> {
    const response = await axios.get(
      `${this.tonnelApiUrl}/floor/${modelKey}`,
      {
        timeout: 10000,
        headers: {
          'User-Agent': 'PEPE-CAS/1.0'
        }
      }
    );
    
    if (response.status === 404) {
      throw new Error('Model not found');
    }
    
    const data = response.data as FloorPriceResponse;
    
    if (!data.floorPrice || data.currency !== 'TON') {
      throw new Error('Invalid response format');
    }
    
    return data.floorPrice;
  }
  
  /**
   * Fetch floor price from Portal API (fallback)
   */
  private async fetchPortalPrice(modelKey: string): Promise<number> {
    const response = await axios.get(
      `${this.portalApiUrl}/floor/${modelKey}`,
      {
        timeout: 10000,
        headers: {
          'User-Agent': 'PEPE-CAS/1.0'
        }
      }
    );
    
    if (response.status === 404) {
      throw new Error('Model not found');
    }
    
    const data = response.data;
    
    if (!data.floor_price_ton) {
      throw new Error('Invalid response format');
    }
    
    return parseFloat(data.floor_price_ton);
  }
  
  /**
   * Get current floor price for a model
   */
  async getModelPrice(modelKey: string): Promise<number | null> {
    try {
      const price = await prisma.price.findUnique({
        where: { modelKey }
      });
      
      return price ? parseFloat(price.minTon.toString()) : null;
    } catch (error) {
      logger.error(`Error fetching price for ${modelKey}:`, error);
      return null;
    }
  }
  
  /**
   * Get all current floor prices
   */
  async getAllPrices(): Promise<Record<string, number>> {
    try {
      const prices = await prisma.price.findMany();
      
      const result: Record<string, number> = {};
      for (const price of prices) {
        result[price.modelKey] = parseFloat(price.minTon.toString());
      }
      
      return result;
    } catch (error) {
      logger.error('Error fetching all prices:', error);
      return {};
    }
  }
}
