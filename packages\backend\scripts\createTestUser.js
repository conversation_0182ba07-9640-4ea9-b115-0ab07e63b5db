const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Create a test user
    const user = await prisma.user.upsert({
      where: { telegramId: '123456789' },
      update: {},
      create: {
        telegramId: '123456789',
        username: 'testuser',
        balanceTon: 100.0 // Give 100 TON for testing
      }
    });

    console.log('Test user created:', user);

    // Create some test prices
    const prices = [
      { modelKey: 'delicious_cake', minTon: 1.5 },
      { modelKey: 'green_star', minTon: 3.0 },
      { modelKey: 'blue_star', minTon: 5.0 },
      { modelKey: 'red_star', minTon: 10.0 },
      { modelKey: 'golden_star', minTon: 25.0 }
    ];

    for (const price of prices) {
      await prisma.price.upsert({
        where: { modelKey: price.modelKey },
        update: { minTon: price.minTon },
        create: price
      });
    }

    console.log('Test prices created');

    // Create some test transactions
    await prisma.transaction.create({
      data: {
        kind: 'DEPOSIT_TON',
        amountTon: 100.0,
        ref: 'initial_balance',
        userId: user.id
      }
    });

    console.log('Test data created successfully!');
  } catch (error) {
    console.error('Error creating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
